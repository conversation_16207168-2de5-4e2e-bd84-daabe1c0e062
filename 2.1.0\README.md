# 多语言互译工具

这是一个使用大型语言模型（如Google Gemini、OpenAI等）进行多语言互译的工具。

## 主要功能

- 支持多种语言之间的互译
- 自动检测源语言，智能翻译到目标语言
- 支持自定义翻译模式和样式
- 提供GUI进度显示和快捷键操作
- 支持内存缓存和本地文件缓存

## 翻译缓存功能

本工具支持两级缓存机制，提高翻译效率并节省API调用：

### 内存缓存

- 程序运行期间的短期缓存
- 关闭程序后会被清除
- 可以通过在控制台输入`00`清除

### 本地文件缓存

- 翻译结果保存在本地SQLite数据库中
- 程序重启后依然可用
- 通过设置菜单可以管理本地缓存

## 缓存管理设置

在设置菜单中选择"缓存管理设置"可以进行以下操作：

1. 开启/关闭本地缓存 - 控制是否使用本地文件缓存
2. 设置缓存优先级 - 选择优先使用缓存还是大模型
3. 修改本地缓存路径 - 自定义缓存数据库的存储位置
4. 修改最大缓存条目数 - 限制缓存的条目数量
5. 修改缓存写入延迟 - 控制批量写入的时间间隔
6. 立即保存所有缓存 - 手动触发缓存写入
7. 清空所有缓存 - 清除内存和本地缓存

## 使用说明

1. 在控制台输入数字选择翻译模式
2. 按三次空格键触发翻译
3. 输入`0`进入设置菜单
4. 输入`00`清除上下文和内存缓存（不会清除本地缓存）
5. 在设置菜单中选择`21`进入缓存管理设置

## 注意事项

- 本地缓存默认保存在程序同一目录下的`translation_cache.db`文件中
- 清除上下文操作（输入`00`）只会清除内存缓存，不会清除本地缓存
- 程序关闭时会自动保存待处理的缓存数据
- 当缓存项数量超过设定的最大值时，会自动清理最旧的条目 