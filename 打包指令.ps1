# 切换到项目目录
Set-Location "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\"

# 执行PyInstaller打包命令
python -m PyInstaller --clean --noconfirm --onefile `
  --add-data "api_crypto.py;." `
  --add-data "config_management.py;." `
  --hidden-import "pyautogui" `
  --hidden-import "keyboard" `
  --hidden-import "pycld2" `
  --hidden-import "cryptography" `
  --icon "图标.ico" `
  --name "多语言互译器2.1.1" `
  --distpath "C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译" `
  "语言互译.py"