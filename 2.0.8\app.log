2025-05-22 18:09:49,537 - INFO - 已创建带注释的默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-22 18:09:49,538 - INFO - 首次运行已创建默认配置文件，初次翻译可能有轻微延迟
2025-05-22 18:09:49,547 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-22 18:09:49,547 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-22 18:09:54,657 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-22 18:09:54,658 - INFO - 已将加密后的API密钥保存到配置文件
2025-05-22 18:40:58,288 - INFO - 已创建带注释的默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-22 18:40:58,289 - INFO - 首次运行已创建默认配置文件，初次翻译可能有轻微延迟
2025-05-22 18:40:58,298 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-22 18:40:58,298 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-22 18:41:02,613 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-22 18:41:02,614 - INFO - 已将加密后的API密钥保存到配置文件
2025-05-24 12:35:36,925 - INFO - 已创建带注释的默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 12:35:36,927 - INFO - 首次运行已创建默认配置文件，初次翻译可能有轻微延迟
2025-05-24 12:35:36,935 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-24 12:35:36,935 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-24 12:36:00,892 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 12:36:00,893 - INFO - 已将加密后的API密钥保存到配置文件
2025-05-24 12:52:44,266 - INFO - 已创建带注释的默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 12:52:44,266 - INFO - 首次运行已创建默认配置文件，初次翻译可能有轻微延迟
2025-05-24 12:52:44,272 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-24 12:52:44,272 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-24 12:53:03,422 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 12:53:03,423 - INFO - 已将加密后的API密钥保存到配置文件
2025-05-24 13:18:26,965 - INFO - 已创建带注释的默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 13:18:26,965 - INFO - 首次运行已创建默认配置文件，初次翻译可能有轻微延迟
2025-05-24 13:18:26,971 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-24 13:18:26,971 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-24 13:18:41,128 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 13:18:41,129 - INFO - 已将加密后的API密钥保存到配置文件
2025-05-24 13:21:48,501 - INFO - 已创建带注释的默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 13:21:48,501 - INFO - 首次运行已创建默认配置文件，初次翻译可能有轻微延迟
2025-05-24 13:21:48,508 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-24 13:21:48,508 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-24 13:21:52,743 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\自定义多语言翻译\迁移文件\config.yaml
2025-05-24 13:21:52,744 - INFO - 已将加密后的API密钥保存到配置文件
2025-05-29 10:46:08,470 - INFO - 主配置已加载。调试模式: True
2025-05-29 10:46:08,604 - INFO - 语言模式配置已加载。
2025-05-29 10:46:08,605 - DEBUG - 创建LRU缓存，容量: 100
2025-05-29 10:46:08,605 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 10:46:08,605 - DEBUG - 创建LRU缓存，容量: 50
2025-05-29 10:46:08,605 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 10:46:08,605 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 10:46:08,605 - DEBUG - Using proactor: IocpProactor
2025-05-29 10:46:08,606 - DEBUG - 创建翻译器主窗口
2025-05-29 10:46:08,606 - DEBUG - 初始化翻译器主窗口设置
2025-05-29 10:46:08,606 - DEBUG - 主窗口设置完成
2025-05-29 10:46:08,607 - DEBUG - 设置键盘监听器
2025-05-29 10:46:08,664 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 10:46:08,683 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 10:46:08,684 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 10:46:09,428 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 10:46:20,813 - INFO - 检测到三次空格，触发翻译
2025-05-29 10:46:20,816 - INFO - 【原文】
ㅎㅎㅎ오래 걸리나요?
2025-05-29 10:46:20,818 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3813.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:46:20,819 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.836)]
2025-05-29 10:46:20,819 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8356, 提示: None)
2025-05-29 10:46:20,819 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 10:46:20,819 - INFO - 检测到原文语言: ko
2025-05-29 10:46:20,820 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 10:46:20,820 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 10:46:20,820 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-29 10:46:20,820 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-29 10:46:20,820 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-29 10:46:20,820 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
ㅎㅎㅎ오래 걸리나요?
2025-05-29 10:46:20,822 - DEBUG - 【构建提示词】长度: 597 字符
2025-05-29 10:46:20,823 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 10:46:20,837 - DEBUG - 显示进度指示器
2025-05-29 10:46:21,054 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 10:46:21,054 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 10:46:21,911 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 95, 1736.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:46:21,911 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.847)]
2025-05-29 10:46:21,912 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.8467, 提示: None)
2025-05-29 10:46:21,912 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 10:46:21,912 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 10:46:21,912 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 10:46:21,913 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh--7553112630699509088
2025-05-29 10:46:21,913 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-2690715377842478770
2025-05-29 10:46:21,914 - INFO - 【翻译结果】
呵呵，需要很久吗？
2025-05-29 10:46:21,914 - DEBUG - 记录上次翻译目标语言: zh
2025-05-29 10:46:22,383 - DEBUG - 输入框内容已替换为: 呵呵，需要很久吗？
2025-05-29 10:46:22,384 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 10:46:22,400 - DEBUG - 隐藏GUI等待提示
2025-05-29 10:46:22,477 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:22,493 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:22,494 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:22,540 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:22,541 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:56,800 - INFO - 检测到三次空格，触发翻译
2025-05-29 10:46:56,802 - INFO - 【原文】
啊，碳配额交易不需要多长时间，我现在每天要做三次
2025-05-29 10:46:56,803 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1929.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:46:56,804 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.961)]
2025-05-29 10:46:56,804 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9610, 提示: None)
2025-05-29 10:46:56,804 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 10:46:56,805 - INFO - 检测到原文语言: zh
2025-05-29 10:46:56,805 - INFO - 执行正向翻译为: ko
2025-05-29 10:46:56,805 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 10:46:56,805 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-29 10:46:56,805 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-29 10:46:56,806 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-29 10:46:56,806 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: ㅎㅎㅎ오래 걸리나요?
翻译: 呵呵，需要很久吗？

将以下内容从中文翻译成韩文，使用敬语：
啊，碳配额交易不需要多长时间，我现在每天要做三次
2025-05-29 10:46:56,807 - DEBUG - 【构建提示词】长度: 677 字符
2025-05-29 10:46:56,808 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 10:46:56,811 - DEBUG - 显示进度指示器
2025-05-29 10:46:57,026 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 10:46:57,027 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 10:46:57,994 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3588.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:46:57,994 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.895)]
2025-05-29 10:46:57,995 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8950, 提示: None)
2025-05-29 10:46:57,995 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 10:46:57,995 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 10:46:57,995 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 10:46:57,996 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko-8999223103606997664
2025-05-29 10:46:57,996 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--1848558207802591497
2025-05-29 10:46:57,996 - INFO - 【翻译结果】
아, 탄소 배출권 거래는 그리 오래 걸리지 않습니다. 지금은 매일 세 번씩 해야 합니다.
2025-05-29 10:46:57,997 - DEBUG - 记录上次翻译目标语言: ko
2025-05-29 10:46:58,453 - DEBUG - 输入框内容已替换为: 아, 탄소 배출권 거래는 그리 오래 걸리지 않습니다. 지금은 매일 세 번씩 해야 합니다.
2025-05-29 10:46:58,454 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 10:46:58,459 - DEBUG - 隐藏GUI等待提示
2025-05-29 10:46:58,619 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:58,619 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:58,633 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:58,650 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:46:58,650 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:55:51,167 - INFO - 检测到三次空格，触发翻译
2025-05-29 10:55:51,169 - INFO - 【原文】
밖에서 한번 보실래요? ㅎㅎ
2025-05-29 10:55:51,170 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3653.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:55:51,170 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.859)]
2025-05-29 10:55:51,170 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8590, 提示: None)
2025-05-29 10:55:51,171 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 10:55:51,171 - INFO - 检测到原文语言: ko
2025-05-29 10:55:51,171 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 10:55:51,172 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 10:55:51,172 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-29 10:55:51,172 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-29 10:55:51,172 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-29 10:55:51,173 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-29 10:55:51,173 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: ㅎㅎㅎ오래 걸리나요?
翻译: 呵呵，需要很久吗？
原文: 啊，碳配额交易不需要多长时间，我现在每天要做三次
翻译: 아, 탄소 배출권 거래는 그리 오래 걸리지 않습니다. 지금은 매일 세 번씩 해야 합니다.

将以下内容从韩文翻译成中文：
밖에서 한번 보실래요? ㅎㅎ
2025-05-29 10:55:51,174 - DEBUG - 【构建提示词】长度: 741 字符
2025-05-29 10:55:51,175 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 10:55:51,183 - DEBUG - 显示进度指示器
2025-05-29 10:55:51,369 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 10:55:51,370 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 10:55:53,536 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:55:53,537 - DEBUG - 基于特征补充候选: zh (score: 0.3750)
2025-05-29 10:55:53,538 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.375)]
2025-05-29 10:55:53,538 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.3750, 提示: None)
2025-05-29 10:55:53,539 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 10:55:53,539 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 10:55:53,540 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 10:55:53,540 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-8407683939854880783
2025-05-29 10:55:53,540 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-590575776197982092
2025-05-29 10:55:53,541 - INFO - 【翻译结果】
要不要出去看看？ 嘿嘿。
2025-05-29 10:55:53,541 - DEBUG - 记录上次翻译目标语言: zh
2025-05-29 10:55:53,998 - DEBUG - 输入框内容已替换为: 要不要出去看看？ 嘿嘿。
2025-05-29 10:55:53,999 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 10:55:54,002 - DEBUG - 隐藏GUI等待提示
2025-05-29 10:55:54,007 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:55:54,148 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:55:54,149 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:55:54,195 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:55:54,196 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:56:37,010 - INFO - 检测到三次空格，触发翻译
2025-05-29 10:56:37,011 - INFO - 【原文】
要回家洗澡做交易了哦~下午还要开会呢。
2025-05-29 10:56:37,013 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1874.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:56:37,013 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.954)]
2025-05-29 10:56:37,013 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9544, 提示: None)
2025-05-29 10:56:37,013 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 10:56:37,014 - INFO - 检测到原文语言: zh
2025-05-29 10:56:37,014 - INFO - 执行正向翻译为: ko
2025-05-29 10:56:37,014 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 10:56:37,014 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-29 10:56:37,014 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-29 10:56:37,014 - INFO - 模式 2 当前上下文数量: 3（最大: 8）
2025-05-29 10:56:37,015 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: ㅎㅎㅎ오래 걸리나요?
翻译: 呵呵，需要很久吗？
原文: 啊，碳配额交易不需要多长时间，我现在每天要做三次
翻译: 아, 탄소 배출권 거래는 그리 오래 걸리지 않습니다. 지금은 매일 세 번씩 해야 합니다.
原文: 밖에서 한번 보실래요? ㅎㅎ
翻译: 要不要出去看看？ 嘿嘿。

将以下内容从中文翻译成韩文，使用敬语：
要回家洗澡做交易了哦~下午还要开会呢。
2025-05-29 10:56:37,016 - DEBUG - 【构建提示词】长度: 792 字符
2025-05-29 10:56:37,016 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 10:56:37,019 - DEBUG - 显示进度指示器
2025-05-29 10:56:37,260 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 10:56:37,261 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 10:56:38,914 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 10:56:38,915 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 10:56:41,142 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3623.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 10:56:41,143 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.892)]
2025-05-29 10:56:41,144 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8922, 提示: None)
2025-05-29 10:56:41,144 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 10:56:41,144 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 10:56:41,145 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 10:56:41,145 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--6789127685841556542
2025-05-29 10:56:41,146 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--3297436720403979761
2025-05-29 10:56:41,146 - INFO - 【翻译结果】
집에 가서 씻고 거래해야 해요~ 오후에는 회의도 있고요.ㅎ
2025-05-29 10:56:41,147 - DEBUG - 记录上次翻译目标语言: ko
2025-05-29 10:56:41,601 - DEBUG - 输入框内容已替换为: 집에 가서 씻고 거래해야 해요~ 오후에는 회의도 있고요.ㅎ
2025-05-29 10:56:41,602 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 10:56:41,609 - DEBUG - 隐藏GUI等待提示
2025-05-29 10:56:41,672 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:56:41,720 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:56:41,721 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:56:41,750 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:56:41,764 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 10:56:41,765 - DEBUG - 无法更新位置：窗口已无效
2025-05-29 11:03:57,581 - INFO - 主配置已加载。调试模式: True
2025-05-29 11:03:57,690 - INFO - 语言模式配置已加载。
2025-05-29 11:03:57,690 - DEBUG - 创建LRU缓存，容量: 100
2025-05-29 11:03:57,691 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:03:57,691 - DEBUG - 创建LRU缓存，容量: 50
2025-05-29 11:03:57,691 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:03:57,691 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:03:57,692 - DEBUG - Using proactor: IocpProactor
2025-05-29 11:03:57,692 - DEBUG - 创建翻译器主窗口
2025-05-29 11:03:57,692 - DEBUG - 初始化翻译器主窗口设置
2025-05-29 11:03:57,693 - DEBUG - 主窗口设置完成
2025-05-29 11:03:57,693 - DEBUG - 设置键盘监听器
2025-05-29 11:03:57,811 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 11:03:57,811 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 11:03:57,812 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:03:58,568 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 11:04:03,872 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:04:03,874 - INFO - 【原文】
   네....알겠습니다.
2025-05-29 11:04:03,876 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 95, 3686.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:04:03,876 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.829)]
2025-05-29 11:04:03,877 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8286, 提示: None)
2025-05-29 11:04:03,877 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 11:04:03,877 - INFO - 检测到原文语言: ko
2025-05-29 11:04:03,877 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:04:03,877 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:04:03,877 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-29 11:04:03,877 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-29 11:04:03,878 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-29 11:04:03,878 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
네....알겠습니다.
2025-05-29 11:04:03,879 - DEBUG - 【构建提示词】长度: 597 字符
2025-05-29 11:04:03,880 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 11:04:03,903 - DEBUG - 显示进度指示器
2025-05-29 11:04:04,075 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 11:04:04,076 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 11:04:05,335 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:04:05,336 - DEBUG - 基于特征补充候选: zh (score: 0.2857)
2025-05-29 11:04:05,336 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.286)]
2025-05-29 11:04:05,337 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.2857, 提示: None)
2025-05-29 11:04:05,337 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 11:04:05,338 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:04:05,338 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:04:05,339 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-8583301452486821627
2025-05-29 11:04:05,340 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-4308056529259220050
2025-05-29 11:04:05,341 - INFO - 【翻译结果】
哦……知道了。
2025-05-29 11:04:05,341 - DEBUG - 记录上次翻译目标语言: zh
2025-05-29 11:04:05,808 - DEBUG - 输入框内容已替换为: 哦……知道了。
2025-05-29 11:04:05,809 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:04:05,826 - DEBUG - 隐藏GUI等待提示
2025-05-29 11:04:40,554 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:04:40,556 - INFO - 【原文】
哈哈~还是要多做运动才行。
2025-05-29 11:04:40,558 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1960.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:04:40,558 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.933)]
2025-05-29 11:04:40,559 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9328, 提示: None)
2025-05-29 11:04:40,559 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 11:04:40,559 - INFO - 检测到原文语言: zh
2025-05-29 11:04:40,560 - INFO - 执行正向翻译为: ko
2025-05-29 11:04:40,560 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:04:40,560 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-29 11:04:40,560 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-29 11:04:40,560 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-29 11:04:40,561 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文:    네....알겠습니다.
翻译: 哦……知道了。

将以下内容从中文翻译成韩文，使用敬语：
哈哈~还是要多做运动才行。
2025-05-29 11:04:40,562 - DEBUG - 【构建提示词】长度: 667 字符
2025-05-29 11:04:40,562 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 11:04:40,566 - DEBUG - 显示进度指示器
2025-05-29 11:04:40,780 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 11:04:40,781 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 11:04:41,488 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 11:04:41,489 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 11:04:42,454 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3630.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:04:42,455 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.868)]
2025-05-29 11:04:42,456 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8685, 提示: None)
2025-05-29 11:04:42,457 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 11:04:42,457 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 11:04:42,458 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 11:04:42,459 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko-5614146623598094477
2025-05-29 11:04:42,459 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--2363975510424594184
2025-05-29 11:04:42,460 - INFO - 【翻译结果】
ㅋ~ 역시 운동을 많이 해야겠어요.
2025-05-29 11:04:42,460 - DEBUG - 记录上次翻译目标语言: ko
2025-05-29 11:04:42,914 - DEBUG - 输入框内容已替换为: ㅋ~ 역시 운동을 많이 해야겠어요.
2025-05-29 11:04:42,915 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:04:42,921 - DEBUG - 隐藏GUI等待提示
2025-05-29 11:04:49,499 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:04:49,501 - INFO - 【原文】
넹 ㅎㅎ 전 집에 갈게요
2025-05-29 11:04:49,501 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3456.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:04:49,502 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.834)]
2025-05-29 11:04:49,503 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8335, 提示: None)
2025-05-29 11:04:49,503 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 11:04:49,503 - INFO - 检测到原文语言: ko
2025-05-29 11:04:49,503 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:04:49,503 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:04:49,503 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-29 11:04:49,504 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-29 11:04:49,504 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-29 11:04:49,504 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-29 11:04:49,504 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文:    네....알겠습니다.
翻译: 哦……知道了。
原文: 哈哈~还是要多做运动才行。
翻译: ㅋ~ 역시 운동을 많이 해야겠어요.

将以下内容从韩文翻译成中文：
넹 ㅎㅎ 전 집에 갈게요
2025-05-29 11:04:49,506 - DEBUG - 【构建提示词】长度: 699 字符
2025-05-29 11:04:49,506 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 11:04:49,510 - DEBUG - 显示进度指示器
2025-05-29 11:04:49,761 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 11:04:49,762 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 11:04:50,774 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('ChineseT', 'zh-Hant', 96, 1782.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:04:50,775 - DEBUG - 基于特征补充候选: zh (score: 0.3636)
2025-05-29 11:04:50,776 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.364)]
2025-05-29 11:04:50,777 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.3636, 提示: None)
2025-05-29 11:04:50,777 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 11:04:50,778 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:04:50,778 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:04:50,779 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-3392071221445794989
2025-05-29 11:04:50,779 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-20828842324406630
2025-05-29 11:04:50,780 - INFO - 【翻译结果】
嗯哼，呵呵，我回家了。
2025-05-29 11:04:50,780 - DEBUG - 记录上次翻译目标语言: zh
2025-05-29 11:04:51,237 - DEBUG - 输入框内容已替换为: 嗯哼，呵呵，我回家了。
2025-05-29 11:04:51,238 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:04:51,245 - DEBUG - 隐藏GUI等待提示
2025-05-29 11:05:13,220 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:05:13,221 - INFO - 【原文】
啊。你看病医生怎么说呢？有好转么？
2025-05-29 11:05:13,223 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1911.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:05:13,223 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.926)]
2025-05-29 11:05:13,223 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9261, 提示: None)
2025-05-29 11:05:13,224 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 11:05:13,224 - INFO - 检测到原文语言: zh
2025-05-29 11:05:13,224 - INFO - 执行正向翻译为: ko
2025-05-29 11:05:13,224 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:05:13,224 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-29 11:05:13,225 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-29 11:05:13,225 - INFO - 模式 2 当前上下文数量: 3（最大: 8）
2025-05-29 11:05:13,225 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文:    네....알겠습니다.
翻译: 哦……知道了。
原文: 哈哈~还是要多做运动才行。
翻译: ㅋ~ 역시 운동을 많이 해야겠어요.
原文: 넹 ㅎㅎ 전 집에 갈게요
翻译: 嗯哼，呵呵，我回家了。

将以下内容从中文翻译成韩文，使用敬语：
啊。你看病医生怎么说呢？有好转么？
2025-05-29 11:05:13,227 - DEBUG - 【构建提示词】长度: 747 字符
2025-05-29 11:05:13,227 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 11:05:13,230 - DEBUG - 显示进度指示器
2025-05-29 11:05:13,456 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 11:05:13,457 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 11:05:14,326 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3733.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 11:05:14,327 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.904)]
2025-05-29 11:05:14,328 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9042, 提示: None)
2025-05-29 11:05:14,328 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 11:05:14,329 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 11:05:14,329 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:05:14,330 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--8731402197527549117
2025-05-29 11:05:14,330 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-1527544746380416936
2025-05-29 11:05:14,330 - INFO - 【翻译结果】
아, 진찰받은 의사 선생님은 뭐라고 하셨어요? 호전되었나요?
2025-05-29 11:05:14,330 - DEBUG - 记录上次翻译目标语言: ko
2025-05-29 11:05:14,787 - DEBUG - 输入框内容已替换为: 아, 진찰받은 의사 선생님은 뭐라고 하셨어요? 호전되었나요?
2025-05-29 11:05:14,788 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:05:14,792 - DEBUG - 隐藏GUI等待提示
2025-05-29 11:06:40,465 - ERROR - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 不存在。
2025-05-29 11:06:40,466 - ERROR - 请先运行 'python config_management.py --generate-main' 或 'python config_management.py --generate-all' 来生成默认配置文件。
2025-05-29 11:11:57,103 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 不存在，正在自动生成默认配置...
2025-05-29 11:11:57,112 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:11:57,126 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:11:57,146 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:11:57,147 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:11:57,148 - INFO - 请记得在此文件中填入您的加密API密钥。
2025-05-29 11:11:57,148 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:11:57,161 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:11:57,162 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:11:57,162 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:11:57,162 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:11:57,229 - WARNING - 语言模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml 不存在，正在自动生成默认配置...
2025-05-29 11:11:57,229 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:11:57,238 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:11:57,255 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:11:57,256 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:11:57,257 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:11:57,271 - INFO - 语言模式配置已加载。
2025-05-29 11:11:57,272 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:11:57,272 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:11:57,272 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:11:57,385 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:13:42,294 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:13:42,295 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:13:42,295 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:13:42,295 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:13:42,355 - INFO - 语言模式配置已加载。
2025-05-29 11:13:42,355 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:13:42,355 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:13:42,356 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:13:42,486 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:16:42,353 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:16:42,354 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:16:42,354 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:16:42,354 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:16:42,415 - INFO - 语言模式配置已加载。
2025-05-29 11:16:42,415 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:16:42,416 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:16:42,417 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:16:42,526 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:16:47,783 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:16:47,785 - INFO - 【原文】
[행운] [上午 10:06] 이 책은 작가의 일생 경험과 두 아버지로부터 받은 가치관과 생각들을 이야기하고 있어요.

[5月27日首尔新堂洞的38岁姜炳柱] [上午 10:12] 네 읽어봤습니다 저축보단 투자 ㅎ
2025-05-29 11:16:47,787 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8260, 提示: None)
2025-05-29 11:16:47,787 - INFO - 检测到原文语言: ko
2025-05-29 11:16:47,787 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:16:47,788 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:16:48,023 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API密钥未设置或解密失败。尝试使用备用模型。
2025-05-29 11:16:48,023 - ERROR - 翻译最终失败: 翻译失败：API密钥未设置或解密失败。原始文本将恢复到输入框。
2025-05-29 11:16:48,480 - ERROR - 翻译失败: 翻译失败：API密钥未设置或解密失败
2025-05-29 11:30:42,769 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:30:42,770 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:30:42,771 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:30:42,771 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:30:42,887 - INFO - 语言模式配置已加载。
2025-05-29 11:30:42,888 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:30:42,888 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:30:42,888 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:30:42,969 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:30:53,276 - INFO - 用户请求退出程序
2025-05-29 11:30:53,277 - INFO - 正在关闭翻译程序...
2025-05-29 11:30:53,277 - INFO - 正在关闭翻译器...
2025-05-29 11:32:40,331 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:32:40,331 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:32:40,332 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:32:40,332 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:32:40,437 - INFO - 语言模式配置已加载。
2025-05-29 11:32:40,438 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:32:40,438 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:32:40,438 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:32:40,509 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:32:57,096 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:32:57,098 - INFO - 【原文】
수술을 해야된다고
2025-05-29 11:32:57,100 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9173, 提示: None)
2025-05-29 11:32:57,100 - INFO - 检测到原文语言: ko
2025-05-29 11:32:57,100 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:32:57,100 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:32:57,300 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API密钥未设置或解密失败。尝试使用备用模型。
2025-05-29 11:32:57,301 - ERROR - 翻译最终失败: 翻译失败：API密钥未设置或解密失败。原始文本将恢复到输入框。
2025-05-29 11:32:57,760 - ERROR - 翻译失败: 翻译失败：API密钥未设置或解密失败
2025-05-29 11:34:04,181 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:34:04,181 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:34:04,182 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:34:04,182 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:34:04,286 - INFO - 语言模式配置已加载。
2025-05-29 11:34:04,286 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:34:04,286 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:34:04,287 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:34:04,397 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:35:29,301 - WARNING - API密钥 (api_key) 未在 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 中配置或为空。
2025-05-29 11:35:29,301 - WARNING - 请在运行后编辑配置文件，填入您的加密API密钥。
2025-05-29 11:35:29,302 - WARNING - 您可以使用 api_crypto.py 工具来加密您的原始API密钥。
2025-05-29 11:35:29,302 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:35:29,403 - INFO - 语言模式配置已加载。
2025-05-29 11:35:29,403 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:35:29,404 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:35:29,404 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:35:29,512 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:44:10,381 - INFO - API密钥未配置或为空，需要用户输入
2025-05-29 11:44:10,381 - INFO - 未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。
2025-05-29 11:44:10,381 - INFO - 注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。
2025-05-29 11:45:18,192 - INFO - API密钥验证成功
2025-05-29 11:45:18,213 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:45:18,213 - INFO - 已将加密的API密钥保存到配置文件
2025-05-29 11:45:18,214 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:45:18,319 - INFO - 语言模式配置已加载。
2025-05-29 11:45:18,320 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:45:18,320 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:45:18,321 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:45:18,391 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:45:58,247 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 不存在，正在自动生成默认配置...
2025-05-29 11:45:58,257 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:45:58,270 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:45:58,290 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:45:58,291 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:46:07,506 - ERROR - 输入的不是有效的加密API密钥，请使用api_crypto.py工具进行加密后再输入
2025-05-29 11:46:17,472 - INFO - API密钥验证成功
2025-05-29 11:46:17,493 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:46:17,494 - INFO - 已将加密的API密钥保存到配置文件
2025-05-29 11:46:17,494 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 11:46:17,528 - INFO - 主配置已加载。调试模式: False
2025-05-29 11:46:17,575 - WARNING - 语言模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml 不存在，正在自动生成默认配置...
2025-05-29 11:46:17,576 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:46:17,631 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:46:17,718 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:46:17,720 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:46:17,721 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 11:46:17,776 - INFO - 语言模式配置已加载。
2025-05-29 11:46:17,777 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 11:46:17,777 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 11:46:17,777 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 11:46:17,837 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 11:46:45,442 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:46:45,443 - INFO - 【原文】
수술을 해야된다고

하네요
2025-05-29 11:46:45,447 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9147, 提示: None)
2025-05-29 11:46:45,447 - INFO - 检测到原文语言: ko
2025-05-29 11:46:45,447 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:46:45,448 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:46:47,656 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9050, 提示: None)
2025-05-29 11:46:47,657 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:46:47,657 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:46:47,658 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-7961499821259766285
2025-05-29 11:46:47,659 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--5684837423311272681
2025-05-29 11:46:47,659 - INFO - 【翻译结果】
听说需要做手术。
2025-05-29 11:46:48,132 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:46:59,754 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:46:59,756 - INFO - 【原文】
啊？这么严重么？
2025-05-29 11:46:59,757 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.8300, 提示: None)
2025-05-29 11:46:59,758 - INFO - 检测到原文语言: zh
2025-05-29 11:46:59,758 - INFO - 执行正向翻译为: ko
2025-05-29 11:46:59,758 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:46:59,759 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-29 11:47:01,676 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8790, 提示: None)
2025-05-29 11:47:01,677 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 11:47:01,677 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:47:01,678 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--8259449724614700960
2025-05-29 11:47:01,678 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-8401083486200006101
2025-05-29 11:47:01,679 - INFO - 【翻译结果】
아, 그렇게 심각한 건가요?
2025-05-29 11:47:02,133 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:47:08,435 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:47:08,436 - INFO - 【原文】
이제 당신의 사진도 궁금합니다
2025-05-29 11:47:08,437 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9227, 提示: None)
2025-05-29 11:47:08,438 - INFO - 检测到原文语言: ko
2025-05-29 11:47:08,438 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:47:08,439 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:47:08,439 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-29 11:47:10,271 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 11:47:10,273 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 11:47:12,277 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9559, 提示: None)
2025-05-29 11:47:12,277 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:47:12,278 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 11:47:12,278 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-9208104409610948765
2025-05-29 11:47:12,279 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-692306637174438865
2025-05-29 11:47:12,279 - INFO - 【翻译结果】
现在也开始好奇你的照片了。
2025-05-29 11:47:12,736 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:47:24,852 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:47:24,853 - INFO - 【原文】
啊~你看起来好可爱啊。
2025-05-29 11:47:24,855 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9175, 提示: None)
2025-05-29 11:47:24,855 - INFO - 检测到原文语言: zh
2025-05-29 11:47:24,856 - INFO - 执行正向翻译为: ko
2025-05-29 11:47:24,856 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:47:24,856 - INFO - 模式 2 当前上下文数量: 3（最大: 8）
2025-05-29 11:47:27,015 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8731, 提示: None)
2025-05-29 11:47:27,015 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 11:47:27,016 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:47:27,016 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko-1019707884006399733
2025-05-29 11:47:27,017 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-5418641632713563985
2025-05-29 11:47:27,017 - INFO - 【翻译结果】
아~ 당신, 정말 귀여우시네요.
2025-05-29 11:47:27,477 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:48:06,621 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:48:06,622 - INFO - 【原文】
고마워요

당신의 사진도 보고싶어요
2025-05-29 11:48:06,623 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9228, 提示: None)
2025-05-29 11:48:06,624 - INFO - 检测到原文语言: ko
2025-05-29 11:48:06,624 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:48:06,624 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:48:06,624 - INFO - 模式 2 当前上下文数量: 4（最大: 8）
2025-05-29 11:48:08,545 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9361, 提示: None)
2025-05-29 11:48:08,546 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:48:08,546 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:48:08,547 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-8758288770260401739
2025-05-29 11:48:08,547 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--950108030108252562
2025-05-29 11:48:08,547 - INFO - 【翻译结果】
谢谢你，我也想看看你的照片。
2025-05-29 11:48:09,004 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:48:41,254 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:48:41,256 - INFO - 【原文】
좋은하루되세요ㅎㅎ

그런 사람들이 없어야되는데...
2025-05-29 11:48:41,257 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8896, 提示: None)
2025-05-29 11:48:41,257 - INFO - 检测到原文语言: ko
2025-05-29 11:48:41,257 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:48:41,258 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:48:41,258 - INFO - 模式 2 当前上下文数量: 5（最大: 8）
2025-05-29 11:48:41,975 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 11:48:41,977 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 11:48:43,806 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4400, 提示: None)
2025-05-29 11:48:43,807 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:48:43,808 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 11:48:43,809 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh--5132974122429880511
2025-05-29 11:48:43,810 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--4910466418560231782
2025-05-29 11:48:43,811 - INFO - 【翻译结果】
祝你有个美好的一天哈哈，真希望世上少一些那种人……
2025-05-29 11:48:44,267 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:49:10,999 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:49:11,001 - INFO - 【原文】
我刚才健身房回到家中准备洗澡了，你好好享用午餐吧~
2025-05-29 11:49:11,002 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4600, 提示: None)
2025-05-29 11:49:11,002 - INFO - 检测到原文语言: zh
2025-05-29 11:49:11,003 - INFO - 执行正向翻译为: ko
2025-05-29 11:49:11,003 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:49:11,003 - INFO - 模式 2 当前上下文数量: 6（最大: 8）
2025-05-29 11:49:13,176 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9090, 提示: None)
2025-05-29 11:49:13,177 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 11:49:13,178 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:49:13,179 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--410482397430430003
2025-05-29 11:49:13,180 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--4517397272782671044
2025-05-29 11:49:13,180 - INFO - 【翻译结果】
저는 방금 헬스장에서 집에 돌아와서 샤워할 준비를 하고 있어요. 점심 식사 맛있게 드세요~
2025-05-29 11:49:13,639 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:49:30,633 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:49:30,634 - INFO - 【原文】
아침에 너무 바빴네요
2025-05-29 11:49:30,635 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9175, 提示: None)
2025-05-29 11:49:30,635 - INFO - 检测到原文语言: ko
2025-05-29 11:49:30,636 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 11:49:30,636 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:49:30,636 - INFO - 模式 2 当前上下文数量: 7（最大: 8）
2025-05-29 11:49:31,817 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 11:49:31,819 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 11:49:33,737 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4286, 提示: None)
2025-05-29 11:49:33,737 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 11:49:33,738 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 11:49:33,739 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh--1867871007452713245
2025-05-29 11:49:33,739 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-4490451255113775881
2025-05-29 11:49:33,740 - INFO - 【翻译结果】
早上太忙了呢。
2025-05-29 11:49:34,198 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 11:49:59,929 - INFO - 检测到三次空格，触发翻译
2025-05-29 11:49:59,930 - INFO - 【原文】
啊。我才从健身房回来，在做碳配额交易。
2025-05-29 11:49:59,931 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9386, 提示: None)
2025-05-29 11:49:59,932 - INFO - 检测到原文语言: zh
2025-05-29 11:49:59,932 - INFO - 执行正向翻译为: ko
2025-05-29 11:49:59,932 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 11:49:59,932 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 11:50:02,218 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8992, 提示: None)
2025-05-29 11:50:02,219 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 11:50:02,220 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 11:50:02,221 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--6304568347119464834
2025-05-29 11:50:02,221 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-3889586895373238969
2025-05-29 11:50:02,221 - INFO - 【翻译结果】
아, 저는 방금 헬스장에서 돌아와서 탄소 배출권 거래를 하고 있어요.
2025-05-29 11:50:02,678 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:00:43,199 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:00:43,200 - INFO - 【原文】
당신은 정말아름답네요

첫번째 사진은 어디서 찍은사진인가요?
2025-05-29 12:00:43,202 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9224, 提示: None)
2025-05-29 12:00:43,202 - INFO - 检测到原文语言: ko
2025-05-29 12:00:43,203 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:00:43,203 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:00:43,203 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:00:44,080 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:00:44,082 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:00:44,936 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4412, 提示: None)
2025-05-29 12:00:44,937 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:00:44,938 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:00:44,938 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-8741274852987684791
2025-05-29 12:00:44,939 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-4354532907056057937
2025-05-29 12:00:44,940 - INFO - 【翻译结果】
你真漂亮，第一张照片是在哪里拍的？
2025-05-29 12:00:45,398 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:01:21,248 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:01:21,249 - INFO - 【原文】
巴黎卢浮宫，去看文物收藏展览。
2025-05-29 12:01:21,250 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9390, 提示: None)
2025-05-29 12:01:21,251 - INFO - 检测到原文语言: zh
2025-05-29 12:01:21,252 - INFO - 执行正向翻译为: ko
2025-05-29 12:01:21,252 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:01:21,252 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:01:22,567 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9082, 提示: None)
2025-05-29 12:01:22,568 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:01:22,568 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:01:22,569 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko-4585849817480637491
2025-05-29 12:01:22,570 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-1051318003629429797
2025-05-29 12:01:22,570 - INFO - 【翻译结果】
파리 루브르 박물관에서 유물 전시를 보러 가세요.
2025-05-29 12:01:23,028 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:01:34,592 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:01:34,594 - INFO - 【原文】
미화는 바쁘죠?
2025-05-29 12:01:34,595 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8300, 提示: None)
2025-05-29 12:01:34,595 - INFO - 检测到原文语言: ko
2025-05-29 12:01:34,595 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:01:34,595 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:01:34,596 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:01:35,326 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:01:35,327 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:01:36,322 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.8760, 提示: None)
2025-05-29 12:01:36,323 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:01:36,324 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:01:36,325 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-7339380969814999284
2025-05-29 12:01:36,325 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-7810445610847508972
2025-05-29 12:01:36,326 - INFO - 【翻译结果】
美华很忙吧？
2025-05-29 12:01:36,787 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:03:10,715 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:03:10,717 - INFO - 【原文】
啊。有点忙呢~辛苦你了，晚上我给姨父开个视频。确认一下碳配额交易的事情，顺便也和他说说你，之后带你做碳配额交易，你太辛苦了，都要动手术了，需要很多钱。
2025-05-29 12:03:10,718 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9530, 提示: None)
2025-05-29 12:03:10,718 - INFO - 检测到原文语言: zh
2025-05-29 12:03:10,718 - INFO - 执行正向翻译为: ko
2025-05-29 12:03:10,719 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:03:10,719 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:03:13,277 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9022, 提示: None)
2025-05-29 12:03:13,278 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:03:13,279 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:03:13,280 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--6692157662566291889
2025-05-29 12:03:13,280 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--2018017296980523266
2025-05-29 12:03:13,281 - INFO - 【翻译结果】
아, 좀 바쁘네요~ 수고하셨어요, 저녁에 이모부께 영상 통화 걸게요. 탄소 배출권 거래 관련해서 확인하고, 당신 얘기도 좀 해 드릴게요. 나중에 당신을 데리고 탄소 배출권 거래를 할게요. 당신이 너무 고생해서 수술까지 해야 하니, 돈이 많이 필요하잖아요.
2025-05-29 12:03:13,740 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:03:18,412 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:03:18,413 - INFO - 【原文】
점심 먹으로 갈꺼야  점심 맛나게 먹어
2025-05-29 12:03:18,414 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9003, 提示: None)
2025-05-29 12:03:18,414 - INFO - 检测到原文语言: ko
2025-05-29 12:03:18,415 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:03:18,415 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:03:18,416 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:03:19,682 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4375, 提示: None)
2025-05-29 12:03:19,683 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:03:19,683 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:03:19,684 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh--7355636958916670138
2025-05-29 12:03:19,684 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-7348871470445437493
2025-05-29 12:03:19,685 - INFO - 【翻译结果】
我要去吃午饭啦，午饭要吃得香哦。
2025-05-29 12:03:20,146 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:03:56,837 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:03:56,837 - INFO - 【原文】
你也好好享用午餐哦~
2025-05-29 12:03:56,838 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4500, 提示: None)
2025-05-29 12:03:56,839 - INFO - 检测到原文语言: zh
2025-05-29 12:03:56,839 - INFO - 执行正向翻译为: ko
2025-05-29 12:03:56,839 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:03:56,839 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:03:57,900 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8957, 提示: None)
2025-05-29 12:03:57,901 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:03:57,901 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:03:57,903 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--1307152140033672289
2025-05-29 12:03:57,903 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-6905518988600765969
2025-05-29 12:03:57,903 - INFO - 【翻译结果】
당신도 점심 식사 맛있게 드세요~
2025-05-29 12:03:58,365 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:04:12,413 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:04:12,414 - INFO - 【原文】
점심에 산책 갔다오겠습니당
2025-05-29 12:04:12,415 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9361, 提示: None)
2025-05-29 12:04:12,415 - INFO - 检测到原文语言: ko
2025-05-29 12:04:12,416 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:04:12,416 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:04:12,416 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:04:14,404 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4286, 提示: None)
2025-05-29 12:04:14,404 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:04:14,405 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:04:14,406 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh--4300106674421326376
2025-05-29 12:04:14,406 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-1769312486769702745
2025-05-29 12:04:14,407 - INFO - 【翻译结果】
中午去散步啦。
2025-05-29 12:04:14,864 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:04:34,078 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:04:34,079 - INFO - 【原文】
啊~加油忙完准备吃午餐了，你也好好享用午餐吧。
2025-05-29 12:04:34,080 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4348, 提示: None)
2025-05-29 12:04:34,081 - INFO - 检测到原文语言: zh
2025-05-29 12:04:34,081 - INFO - 执行正向翻译为: ko
2025-05-29 12:04:34,081 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:04:34,081 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:04:34,794 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:04:34,795 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:04:35,796 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9082, 提示: None)
2025-05-29 12:04:35,797 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:04:35,797 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:04:35,797 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--8885565068162611132
2025-05-29 12:04:35,798 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--2024131423301785
2025-05-29 12:04:35,798 - INFO - 【翻译结果】
아~ 힘내서 마무리하고 점심 먹을 준비해야겠어요. 당신도 점심 식사 맛있게 드세요~
2025-05-29 12:04:36,253 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:06:29,498 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:06:29,500 - INFO - 【原文】
中午吃鸭肉哦~
2025-05-29 12:06:29,501 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4286, 提示: None)
2025-05-29 12:06:29,501 - INFO - 检测到原文语言: zh
2025-05-29 12:06:29,501 - INFO - 执行正向翻译为: ko
2025-05-29 12:06:29,501 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:06:29,502 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:06:31,216 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:06:31,218 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:06:32,135 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9040, 提示: None)
2025-05-29 12:06:32,136 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:06:32,136 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:06:32,137 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--7916433796436496906
2025-05-29 12:06:32,137 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--2837466195349865263
2025-05-29 12:06:32,138 - INFO - 【翻译结果】
점심에 오리고기 먹을 거예요~
2025-05-29 12:06:32,598 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:06:36,466 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:06:36,467 - INFO - 【原文】
점심 맛있게드세요
2025-05-29 12:06:36,468 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9173, 提示: None)
2025-05-29 12:06:36,469 - INFO - 检测到原文语言: ko
2025-05-29 12:06:36,469 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:06:36,469 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:06:36,469 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:06:37,189 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:06:37,191 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:06:38,398 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4375, 提示: None)
2025-05-29 12:06:38,398 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:06:38,399 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:06:38,400 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-8439728504282642038
2025-05-29 12:06:38,400 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--941867007808076298
2025-05-29 12:06:38,401 - INFO - 【翻译结果】
午饭要好好吃哦~
2025-05-29 12:06:38,859 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:06:52,771 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:06:52,772 - INFO - 【原文】
嗯，你也好好享用午餐哦~
2025-05-29 12:06:52,773 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4167, 提示: None)
2025-05-29 12:06:52,773 - INFO - 检测到原文语言: zh
2025-05-29 12:06:52,774 - INFO - 执行正向翻译为: ko
2025-05-29 12:06:52,774 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:06:52,774 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:06:55,037 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8790, 提示: None)
2025-05-29 12:06:55,038 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:06:55,038 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:06:55,039 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko-1262167997010509036
2025-05-29 12:06:55,039 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-8851862304427721274
2025-05-29 12:06:55,040 - INFO - 【翻译结果】
음, 당신도 점심 식사 맛있게 드세요~
2025-05-29 12:06:55,499 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:07:09,450 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:07:09,451 - INFO - 【原文】
함께할수있으면 좋겠네요
2025-05-29 12:07:09,452 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9540, 提示: None)
2025-05-29 12:07:09,452 - INFO - 检测到原文语言: ko
2025-05-29 12:07:09,452 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:07:09,453 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:07:09,453 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:07:10,467 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4375, 提示: None)
2025-05-29 12:07:10,468 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:07:10,468 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:07:10,469 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-3173411473450879725
2025-05-29 12:07:10,469 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--4229195484872431064
2025-05-29 12:07:10,469 - INFO - 【翻译结果】
能一起就好了呀。
2025-05-29 12:07:10,924 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:07:34,323 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:07:34,325 - INFO - 【原文】
我只以结婚为目的交往。
2025-05-29 12:07:34,327 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9447, 提示: None)
2025-05-29 12:07:34,327 - INFO - 检测到原文语言: zh
2025-05-29 12:07:34,327 - INFO - 执行正向翻译为: ko
2025-05-29 12:07:34,327 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:07:34,327 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:07:35,110 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:07:35,111 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:07:36,294 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9123, 提示: None)
2025-05-29 12:07:36,295 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:07:36,295 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:07:36,296 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--3052093139516524261
2025-05-29 12:07:36,297 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-3588718210132206162
2025-05-29 12:07:36,298 - INFO - 【翻译结果】
저는 결혼을 전제로만 교제합니다.
2025-05-29 12:07:36,758 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:07:42,131 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:07:42,133 - INFO - 【原文】
不然只能做朋友。
2025-05-29 12:07:42,134 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4375, 提示: None)
2025-05-29 12:07:42,135 - INFO - 检测到原文语言: zh
2025-05-29 12:07:42,135 - INFO - 执行正向翻译为: ko
2025-05-29 12:07:42,136 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:07:42,136 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:07:42,889 - ERROR - API服务暂时不可用 (503)，模型: gemini-2.0-flash-lite-preview-02-05。信息: {
  "error": {
    "code": 503,
    "message": "The model is overloaded. Please try again later.",
    "status": "UNAVAILABLE"
  }
}

2025-05-29 12:07:42,891 - WARNING - 主模型 (gemini-2.0-flash-lite-preview-02-05) 翻译失败: 翻译失败：API服务暂时不可用 (503) (模型: gemini-2.0-flash-lite-preview-02-05)。尝试使用备用模型。
2025-05-29 12:07:43,981 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9077, 提示: None)
2025-05-29 12:07:43,982 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:07:43,982 - INFO - API翻译成功。模型: gemini-2.0-flash
2025-05-29 12:07:43,983 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko-7781461635882555520
2025-05-29 12:07:43,983 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh-5221762462570035593
2025-05-29 12:07:43,984 - INFO - 【翻译结果】
그렇지 않으면 친구로 지낼 수밖에 없어요.
2025-05-29 12:07:44,440 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:09:31,625 - INFO - 配置文件已更新: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 12:09:31,626 - INFO - 调试模式已开启
2025-05-29 12:09:44,575 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:09:44,576 - INFO - 【原文】
ㅎㅎㅎ 바쁘시니까 그럼 전 다시 당진으로 내려갈게요

미화 못봐서 아쉽네요
2025-05-29 12:09:44,577 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3698.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 12:09:44,577 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.898)]
2025-05-29 12:09:44,577 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.8979, 提示: None)
2025-05-29 12:09:44,577 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 12:09:44,577 - INFO - 检测到原文语言: ko
2025-05-29 12:09:44,578 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 12:09:44,578 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:09:44,578 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-29 12:09:44,578 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-29 12:09:44,578 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-29 12:09:44,578 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:09:44,578 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 점심에 산책 갔다오겠습니당
翻译: 中午去散步啦。
原文: 啊~加油忙完准备吃午餐了，你也好好享用午餐吧。
翻译: 아~ 힘내서 마무리하고 점심 먹을 준비해야겠어요. 당신도 점심 식사 맛있게 드세요~
原文: 中午吃鸭肉哦~
翻译: 점심에 오리고기 먹을 거예요~
原文: 점심 맛있게드세요
翻译: 午饭要好好吃哦~
原文: 嗯，你也好好享用午餐哦~
翻译: 음, 당신도 점심 식사 맛있게 드세요~
原文: 함께할수있으면 좋겠네요
翻译: 能一起就好了呀。
原文: 我只以结婚为目的交往。
翻译: 저는 결혼을 전제로만 교제합니다.
原文: 不然只能做朋友。
翻译: 그렇지 않으면 친구로 지낼 수밖에 없어요.

将以下内容从韩文翻译成中文：
ㅎㅎㅎ 바쁘시니까 그럼 전 다시 당진으로 내려갈게요 미화 못봐서 아쉽네요
2025-05-29 12:09:44,580 - DEBUG - 【构建提示词】长度: 976 字符
2025-05-29 12:09:44,580 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 12:09:44,584 - DEBUG - 显示进度指示器
2025-05-29 12:09:44,787 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 12:09:44,788 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 12:09:45,909 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 12:09:45,910 - DEBUG - 基于特征补充候选: zh (score: 0.4107)
2025-05-29 12:09:45,910 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.411)]
2025-05-29 12:09:45,911 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.4107, 提示: None)
2025-05-29 12:09:45,912 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 12:09:45,912 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 12:09:45,913 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:09:45,913 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-5831357134556861566
2025-05-29 12:09:45,913 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--6120803347730542496
2025-05-29 12:09:45,914 - INFO - 【翻译结果】
哈哈哈，您很忙，那我就回唐津了。没能见到美花，真可惜呀。
2025-05-29 12:09:45,914 - DEBUG - 记录上次翻译目标语言: zh
2025-05-29 12:09:46,372 - DEBUG - 输入框内容已替换为: 哈哈哈，您很忙，那我就回唐津了。没能见到美花，真可惜呀。
2025-05-29 12:09:46,373 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:09:46,378 - DEBUG - 隐藏GUI等待提示
2025-05-29 12:10:13,888 - INFO - 检测到三次空格，触发翻译
2025-05-29 12:10:13,888 - INFO - 【原文】
你什么时候需要动手术？
2025-05-29 12:10:13,889 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2114.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 12:10:13,890 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.945)]
2025-05-29 12:10:13,890 - INFO - 决策逻辑：最终判定语言: zh (综合分数: 0.9447, 提示: None)
2025-05-29 12:10:13,890 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-29 12:10:13,891 - INFO - 检测到原文语言: zh
2025-05-29 12:10:13,891 - INFO - 执行正向翻译为: ko
2025-05-29 12:10:13,891 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 12:10:13,891 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-29 12:10:13,891 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-29 12:10:13,891 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-29 12:10:13,892 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 啊~加油忙完准备吃午餐了，你也好好享用午餐吧。
翻译: 아~ 힘내서 마무리하고 점심 먹을 준비해야겠어요. 당신도 점심 식사 맛있게 드세요~
原文: 中午吃鸭肉哦~
翻译: 점심에 오리고기 먹을 거예요~
原文: 점심 맛있게드세요
翻译: 午饭要好好吃哦~
原文: 嗯，你也好好享用午餐哦~
翻译: 음, 당신도 점심 식사 맛있게 드세요~
原文: 함께할수있으면 좋겠네요
翻译: 能一起就好了呀。
原文: 我只以结婚为目的交往。
翻译: 저는 결혼을 전제로만 교제합니다.
原文: 不然只能做朋友。
翻译: 그렇지 않으면 친구로 지낼 수밖에 없어요.
原文: ㅎㅎㅎ 바쁘시니까 그럼 전 다시 당진으로 내려갈게요

미화 못봐서 아쉽네요
翻译: 哈哈哈，您很忙，那我就回唐津了。没能见到美花，真可惜呀。

将以下内容从中文翻译成韩文，使用敬语：
你什么时候需要动手术？
2025-05-29 12:10:13,894 - DEBUG - 【构建提示词】长度: 1005 字符
2025-05-29 12:10:13,894 - DEBUG - 进度指示器未显示，无法更新状态
2025-05-29 12:10:13,897 - DEBUG - 显示进度指示器
2025-05-29 12:10:14,132 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 12:10:14,132 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 12:10:15,021 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3660.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-29 12:10:15,022 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.9)]
2025-05-29 12:10:15,023 - INFO - 决策逻辑：最终判定语言: ko (综合分数: 0.9001, 提示: None)
2025-05-29 12:10:15,024 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-29 12:10:15,024 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-29 12:10:15,024 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 12:10:15,025 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-zh-ko--7772587932404735402
2025-05-29 12:10:15,025 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-ko-zh--9049908732119613317
2025-05-29 12:10:15,025 - INFO - 【翻译结果】
수술은 언제 받으셔야 하는 건가요?
2025-05-29 12:10:15,025 - DEBUG - 记录上次翻译目标语言: ko
2025-05-29 12:10:15,482 - DEBUG - 输入框内容已替换为: 수술은 언제 받으셔야 하는 건가요?
2025-05-29 12:10:15,483 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 12:10:15,490 - DEBUG - 隐藏GUI等待提示
2025-05-29 12:24:49,366 - INFO - 主配置已加载。调试模式: True
2025-05-29 12:24:49,475 - INFO - 语言模式配置已加载。
2025-05-29 12:24:49,476 - INFO - 语言模式配置已加载。
2025-05-29 12:24:49,476 - DEBUG - 创建LRU缓存，容量: 100
2025-05-29 12:24:49,476 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 12:24:49,476 - DEBUG - 创建LRU缓存，容量: 50
2025-05-29 12:24:49,476 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 12:24:49,476 - INFO - 【初始化】特定语族歧义解决规则: ['cjk']
2025-05-29 12:24:49,476 - DEBUG - Using proactor: IocpProactor
2025-05-29 12:24:49,477 - DEBUG - 创建翻译器主窗口
2025-05-29 12:24:49,477 - DEBUG - 初始化翻译器主窗口设置
2025-05-29 12:24:49,477 - DEBUG - 主窗口设置完成
2025-05-29 12:24:49,477 - DEBUG - 设置键盘监听器
2025-05-29 12:24:49,560 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 12:24:49,571 - DEBUG - 网络连接正常 (8.8.8.8)
2025-05-29 12:24:49,572 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 12:24:50,425 - DEBUG - 使用缓存的网络状态: 已连接
2025-05-29 12:25:00,466 - INFO - 用户请求退出程序
2025-05-29 12:25:00,466 - INFO - 正在关闭翻译程序...
2025-05-29 12:25:00,467 - INFO - 正在关闭翻译器...
2025-05-29 12:25:00,467 - DEBUG - 翻译器资源已释放
2025-05-29 13:29:05,432 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml 不存在，正在自动生成默认配置...
2025-05-29 13:29:05,432 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 13:29:05,446 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 13:29:05,469 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 13:29:05,470 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 13:29:30,426 - INFO - API密钥验证成功
2025-05-29 13:29:30,448 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 13:29:30,449 - INFO - 已将加密的API密钥保存到配置文件
2025-05-29 13:29:30,449 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\config.yaml
2025-05-29 13:29:30,497 - INFO - 主配置已加载。调试模式: False
2025-05-29 13:29:30,545 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml 不存在，正在生成默认配置...
2025-05-29 13:29:30,545 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 13:29:30,600 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 13:29:30,733 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 13:29:30,734 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 13:29:30,735 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.7\mode_config.yaml
2025-05-29 13:29:30,868 - INFO - 语言模式配置已加载。
2025-05-29 13:29:30,869 - INFO - 语言模式配置已加载。
2025-05-29 13:29:30,869 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 13:29:30,870 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 13:29:30,950 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 13:29:55,729 - INFO - 检测到三次空格，触发翻译
2025-05-29 13:29:55,731 - INFO - 【原文】
그냥 사무실에 있어요.요즘경기가 안좋아서 크게 일이 없네요
2025-05-29 13:29:55,734 - INFO - 检测到原文语言: ko
2025-05-29 13:29:55,734 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 13:29:55,734 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 13:29:56,878 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 13:29:56,878 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 13:29:56,879 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh-9063046654655824920
2025-05-29 13:29:56,880 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko-8624138745286299508
2025-05-29 13:29:56,880 - INFO - 【翻译结果】
我只是在办公室里。最近经济不景气，没什么大事。
2025-05-29 13:29:57,351 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 13:30:02,362 - INFO - 检测到三次空格，触发翻译
2025-05-29 13:30:02,363 - INFO - 【原文】
아, 네. 사장님이세요?
2025-05-29 13:30:02,364 - INFO - 检测到原文语言: ko
2025-05-29 13:30:02,365 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-29 13:30:02,365 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-29 13:30:02,365 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-29 13:30:03,389 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-29 13:30:03,390 - INFO - API翻译成功。模型: gemini-2.0-flash-lite-preview-02-05
2025-05-29 13:30:03,390 - INFO - 【缓存更新】翻译结果已存入缓存。Key: 2-ko-zh--485290610926967416
2025-05-29 13:30:03,391 - INFO - 【缓存更新】反向翻译结果已存入缓存。Key: 2-zh-ko--3448841627796026089
2025-05-29 13:30:03,392 - INFO - 【翻译结果】
哦，是，您是老板吗？
2025-05-29 13:30:03,854 - INFO - 翻译完成，结果已替换输入框内容
2025-05-29 14:01:05,437 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml 不存在，正在自动生成默认配置...
2025-05-29 14:01:05,437 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 14:01:05,451 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 14:01:05,472 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 14:01:05,473 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 14:01:21,930 - INFO - API密钥验证成功
2025-05-29 14:01:21,952 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 14:01:21,953 - INFO - 已将加密的API密钥保存到配置文件
2025-05-29 14:01:21,954 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 14:01:22,004 - INFO - 主配置已加载。调试模式: False
2025-05-29 14:01:22,062 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml 不存在，正在生成默认配置...
2025-05-29 14:01:22,063 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 14:01:22,118 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 14:01:22,249 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 14:01:22,251 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 14:01:22,252 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 14:01:22,383 - INFO - 语言模式配置已加载。
2025-05-29 14:01:22,383 - INFO - 语言模式配置已加载。
2025-05-29 14:01:22,384 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 14:01:22,385 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 14:01:22,477 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-29 16:18:52,221 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml 不存在，正在自动生成默认配置...
2025-05-29 16:18:52,222 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 16:18:52,236 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 16:18:52,258 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 16:18:52,259 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 16:19:08,252 - INFO - API密钥验证成功
2025-05-29 16:19:08,274 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 16:19:08,275 - INFO - 已将加密的API密钥保存到配置文件
2025-05-29 16:19:08,276 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\config.yaml
2025-05-29 16:19:08,325 - INFO - 主配置已加载。调试模式: False
2025-05-29 16:19:08,386 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml 不存在，正在生成默认配置...
2025-05-29 16:19:08,386 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 16:19:08,445 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 16:19:08,575 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 16:19:08,576 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 16:19:08,577 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.0.8\mode_config.yaml
2025-05-29 16:19:08,706 - INFO - 语言模式配置已加载。
2025-05-29 16:19:08,707 - INFO - 语言模式配置已加载。
2025-05-29 16:19:08,707 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-29 16:19:08,708 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-29 16:19:08,822 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
