2025-05-31 01:46:42,862 - INFO - 检测到 PyInstaller 打包环境，使用可执行文件目录: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译
2025-05-31 01:46:42,867 - WARNING - 主配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml 不存在，正在自动生成默认配置...
2025-05-31 01:46:42,869 - INFO - 正在生成默认主配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml
2025-05-31 01:46:42,871 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml
2025-05-31 01:46:42,893 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml
2025-05-31 01:46:42,894 - INFO - 默认主配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml
2025-05-31 01:46:48,768 - INFO - API密钥验证成功
2025-05-31 01:46:48,792 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml
2025-05-31 01:46:48,793 - INFO - 已将加密的API密钥保存到配置文件
2025-05-31 01:46:48,793 - INFO - 已成功生成默认配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\config.yaml
2025-05-31 01:46:48,807 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 01:46:48,860 - WARNING - 模式配置文件 C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\mode_config.yaml 不存在，正在生成默认配置...
2025-05-31 01:46:48,862 - INFO - 正在生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\mode_config.yaml
2025-05-31 01:46:48,918 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\mode_config.yaml
2025-05-31 01:46:49,044 - INFO - 模式配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\mode_config.yaml
2025-05-31 01:46:49,048 - INFO - 默认模式配置文件已成功生成: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\mode_config.yaml
2025-05-31 01:46:49,049 - INFO - 已成功生成默认模式配置文件: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\mode_config.yaml
2025-05-31 01:46:49,171 - INFO - 语言模式配置已加载。
2025-05-31 01:46:49,172 - INFO - 语言模式配置已加载。
2025-05-31 01:46:49,173 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 01:46:49,173 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 01:46:49,197 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\translation_cache.db
2025-05-31 01:47:10,650 - INFO - 检测到 PyInstaller 打包环境，使用可执行文件目录: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译
2025-05-31 01:47:10,672 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 01:47:10,843 - INFO - 语言模式配置已加载。
2025-05-31 01:47:10,844 - INFO - 语言模式配置已加载。
2025-05-31 01:47:10,844 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 01:47:10,844 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 01:47:10,858 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\编译\translation_cache.db
2025-05-31 01:47:10,963 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 01:47:12,499 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 01:47:13,718 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 01:47:13,719 - INFO - API服务正常
