import asyncio
from dataclasses import asdict, dataclass, field
import datetime
import json
import math
import os
import socket
import sys
import threading
import time
import tkinter as tk
import tkinter.messagebox as messagebox
import yaml
import aiohttp
import pyautogui
from pynput import keyboard
import regex
from dataclasses import dataclass, field
from typing import Optional, Dict, List, Callable, Any, Tuple
import logging
import pycld2 as cld2
import collections
import pyperclip
import tkinter.ttk as ttk
from difflib import SequenceMatcher
from api_crypto import ApiCrypto
import queue
import ctypes  # 修复ctypes未定义
import gc
import io
from ruamel.yaml import YAML
from ruamel.yaml.scalarstring import PreservedScalarString
from dataclasses import asdict, is_dataclass, fields
from typing import Dict, Any, List, Optional, Union, Callable # 确保导入 Union

# 全局变量
decrypted_api_key = ""
api_crypto = None

# 添加LRU缓存类用于语言检测和翻译缓存
class LRUCache:
    """简单的LRU缓存实现"""
    
    def __init__(self, capacity: int):
        """初始化LRU缓存
        
        Args:
            capacity: 缓存容量上限
        """
        self.cache = collections.OrderedDict()
        self.capacity = capacity
        self._lock = threading.Lock()  # 添加线程锁确保线程安全
        # 添加统计信息
        self.hits = 0
        self.misses = 0
        logger.debug(f"创建LRU缓存，容量: {capacity}")
    
    def get(self, key: str) -> Any:
        """获取缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在则返回None
        """
        with self._lock:
            if key not in self.cache:
                self.misses += 1
                return None
            # 移动到末尾表示最近使用
            self.cache.move_to_end(key)
            self.hits += 1
            # 记录缓存命中信息
            logger.info(f"【缓存命中】键长度: {len(key)}, 值类型: {type(self.cache[key]).__name__}")
            # 记录缓存命中率
            if (self.hits + self.misses) % 10 == 0:  # 每10次请求记录一次
                hit_rate = self.hits / (self.hits + self.misses) * 100
                logger.debug(f"LRU缓存命中率: {hit_rate:.1f}% (命中: {self.hits}, 未命中: {self.misses})")
            return self.cache[key]
    
    def put(self, key: str, value: Any) -> None:
        """添加或更新缓存项
        
        Args:
            key: 缓存键
            value: 要缓存的值
        """
        with self._lock:
            if key in self.cache:
                # 已存在则移到末尾
                self.cache.move_to_end(key)
            elif len(self.cache) >= self.capacity:
                # 缓存已满，移除最久未使用的项
                removed_key, _ = self.cache.popitem(last=False)
                logger.debug(f"缓存已满，移除最久未使用项: {removed_key[:20]}...")
            self.cache[key] = value

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self.cache.clear()
        
    def __len__(self) -> int:
        """返回当前缓存项数量"""
        with self._lock:
            return len(self.cache)

# 全局语言检测缓存
language_detection_cache = None

def init_api_crypto():
    """初始化API加密对象"""
    global api_crypto
    if api_crypto is None:
        from api_crypto import ApiCrypto
        api_crypto = ApiCrypto()
        logger.debug("API加密工具已初始化")
    return api_crypto

# 获取真实API密钥的辅助函数
def get_real_api_key(api_key):
    """获取真实（解密后）的API密钥
    
    Args:
        api_key: 配置中的API密钥（可能是加密的）
        
    Returns:
        str: 解密后的API密钥
    """
    global decrypted_api_key, api_crypto
    
    # 如果API密钥为空，返回空字符串
    if not api_key:
        return ""
    
    # 如果已经解密过，直接返回缓存的结果
    if decrypted_api_key:
        return decrypted_api_key.strip()  # 确保返回时去除空白字符
    
    # 初始化API加密工具
    if not api_crypto:
        init_api_crypto()
    
    # 检查API密钥是否已加密
    if api_crypto.is_encrypted(api_key):
        # 解密API密钥
        decrypted_api_key = api_crypto.decrypt(api_key)
        if decrypted_api_key:
            decrypted_api_key = decrypted_api_key.strip()  # 确保去除空白字符
            logger.debug("API密钥已解密")
            return decrypted_api_key
        else:
            logger.error("API密钥解密失败")
            return ""
    else:
        # 如果不是加密的API密钥，返回无效
        logger.error("API密钥无效：必须使用加密格式的API密钥")
        return ""

# 设置控制台标准输出编码为 UTF-8
from typing import cast
from io import TextIOWrapper

if sys.stdout.encoding is None or sys.stdout.encoding.lower() != 'utf-8':
    try:
        # Python 3.7+ 方法
        if isinstance(sys.stdout, TextIOWrapper) and hasattr(sys, 'reconfigure'):
            # 使用具体类型替代泛型IO
            stdout = cast(TextIOWrapper, sys.stdout)
            if hasattr(stdout, 'reconfigure'):
                stdout.reconfigure(encoding='utf-8')
        else:
            raise AttributeError("reconfigure method not available")
    except AttributeError:
        # 兼容旧版本Python
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

# 日志配置
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Configure logging
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# 文件日志处理器（修复file_handler未定义）
try:
    from logging.handlers import RotatingFileHandler
    log_file = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "app.log")
    file_handler = RotatingFileHandler(log_file, maxBytes=2*1024*1024, backupCount=3, encoding="utf-8")
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.INFO)
    logger.addHandler(file_handler)
except Exception as e:
    file_handler = None
    logger.warning(f"文件日志处理器初始化失败: {e}")

# 配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "config.yaml")
MODE_CONFIG_FILE = os.path.join(os.path.dirname(sys.executable if getattr(sys, 'frozen', False) else __file__), "mode_config.yaml")

# 默认配置文件内容
DEFAULT_CONFIG_TEXT = '''
# 翻译程序主配置文件
# 版本：2.0.5

# API 配置
api_key: ""  # 必须使用加密格式的API密钥，使用api_crypto.py工具加密
model_id: "gemini-2.0-flash-lite-preview-02-05"  # 使用的主模型ID
gemini_fallback_model_id: "gemini-2.0-flash" # Gemini API 备用模型ID
openai_fallback_model_id: "gpt-4.1" # OpenAI API 备用模型ID
api_mode: "gemini"  # API 模式，可选 "gemini"（谷歌API） 或 "openai"（OpenAI兼容API）
api_base_url: "https://api.openai.com"  # OpenAI兼容API的基础URL，仅在 api_mode 为 "openai" 时生效
api_endpoint: "/v1/chat/completions"  # OpenAI兼容API的端点，仅在 api_mode 为 "openai" 时生效

# 模型生成参数
temperature: 0.1  # 模型温度，控制生成文本的随机性，范围 0-2，建议 0-1，默认 0.1
top_p: 0.85  # Top-P 核采样值，控制生成文本的多样性，范围 0-1，建议 0.5-1，默认 0.85
max_output_tokens: 1024  # 生成的最大输出标记数，范围 1-4096，默认 1024
top_k: 30  # Top-K采样，范围 1-100，默认 30
frequency_penalty: 0.0 # 频率惩罚，范围 0-2，默认 0.0
presence_penalty: 0.0 # 存在惩罚，范围 0-2，默认 0.0

# 翻译行为配置
translation_mode: 1  # 默认翻译模式编号，对应 mode_config.yaml 中的模式，建议 1-5
max_text_length: 500  # 最大翻译文本长度（字符数），超过此长度将拒绝翻译，建议 100-1000
context_max_count: 8  # 上下文最大数量，用于保持翻译一致性，范围 0-20，默认 8
short_text_threshold: 10  # 短文本阈值，小于此长度的文本使用优化检测逻辑，建议 5-20
lang_detection_threshold: 0.9  # 语言检测置信度阈值，低于此值将使用特征检查，范围 0-1，默认 0.9

# 网络和请求配置
# TCP连接设置
tcp_connector:
  limit: 10  # 限制同时连接数 
  ttl_dns_cache: 300  # DNS缓存时间（秒）
  keepalive_timeout: 60  # 保持连接活跃时间（秒）

# 超时设置（秒）
timeout:
  total: 10  # 总超时时间
  connect: 3  # 连接超时
  sock_connect: 10  # 套接字连接超时
  sock_read: 20  # 套接字读取超时

# 网络检查设置
network_check:
  hosts: ["*******", "*******"]  # 网络检查主机
  port: 53  # 网络检查端口
  timeout: 2  # 网络检查超时（秒）

# API健康检查设置
api_health_check:
  timeout_total: 10  # 总超时（秒）
  timeout_connect: 5  # 连接超时（秒）
  timeout_sock_connect: 5  # 套接字连接超时（秒）
  timeout_sock_read: 8  # 套接字读取超时（秒）
  test_prompt: "Hello, API check"  # 测试提示词

# 网络配置
request_min_interval: 1.0  # 两次翻译请求之间的最小间隔（秒），防止过于频繁请求，建议 0.5-2，默认 1.0

# 日志和调试
debug_mode: false  # 调试模式，true 为开启（显示详细日志），false 为关闭，默认 false
log_info_max: 50  # 日志文件中 INFO 级别的最大条目数，建议 50-200，默认 50
log_other_max: 50  # 日志文件中非 INFO 级别的最大条目数（WARNING/ERROR），建议 10-50，默认 50

# GUI 配置
show_gui_progress: true  # 是否显示GUI等待提示，true为显示，false为不显示

# 文本过滤配置
common_symbols: '[,.!?;:"''()\\[\\]\\{\\}<>+=*/&@#$%^&*~|_，。！？；：、""''（）【】《》]'  # 通用的常用符号集，正则表达式，翻译中保留这些字符
illegal_chars: '[\\x00-\\x1F\\x7F-\\x9F\\씨]'  # 需要移除的非法字符，正则表达式，例如控制字符

# 安全设置
safety_settings:
  gemini:
    - category: "HARM_CATEGORY_HARASSMENT"
      threshold: "BLOCK_NONE"
    - category: "HARM_CATEGORY_HATE_SPEECH"
      threshold: "BLOCK_NONE"
    - category: "HARM_CATEGORY_SEXUALLY_EXPLICIT"
      threshold: "BLOCK_NONE"
    - category: "HARM_CATEGORY_DANGEROUS_CONTENT"
      threshold: "BLOCK_NONE"

# 语言相关新增配置
language_detection_cache_size: 100  # 语言检测缓存大小
translation_cache_size: 50  # 翻译结果缓存大小
same_language_match_threshold: 0.5  # 检测翻译结果与原文相似度的阈值，范围0-1，越高检测越严格，默认0.5
language_families:  # 语言家族分组
  cjk: ["zh", "ja", "ko"]  # 中日韩语系
  european: ["en", "fr", "de", "es", "it", "pt", "ru"]  # 欧洲语系
  indic: ["hi", "bn", "ur"]  # 印度语系
language_specific_settings: {}  # 每种语言的特定设置

# 中韩文区分配置
ko_zh_detection:
  enabled: True  # 是否启用增强的中韩文区分
  ko_specific_ratio_threshold: 0.3  # 韩文特有结构占比阈值
  lang_feature_score_threshold: 0.3  # 语言特征匹配分数阈值
  feature_dominance_ratio: 2.0  # 一种语言特征明显占优的比例
  cjk_feature_score_threshold: 0.35  # CJK语言族内特征匹配分数阈值

# 通用语气符号配置
universal_punctuation:
  question_marks:
    universal: ["?", "？"]  # 所有语言通用的问号
    latin: ["?"]            # 拉丁语系
    cjk: ["？"]             # 中日韩
  exclamation_marks:
    universal: ["!", "！"]
    latin: ["!"]
    cjk: ["！"]
'''

# 默认模式配置文本内容（YAML格式）
DEFAULT_MODE_CONFIG_TEXT = '''
# 语言模式配置文件
# 版本：2.0.5

# 语气助词配置
tone_particles:
  zh: "[哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]"  # 中文语气助词
  ko: "[ㅋ|ㅎ|아|네|헤|ㅜ]"  # 韩文语气助词
  ja: "[ｗ笑]+"  # 日文语气助词
  en: "[lol|haha|hehe|yeah]"  # 英文语气助词

# 翻译模式配置
translation_modes:
  1:  # 模式1：中韩平语
    source_lang: 中文
    target_lang: 韩文
    style: 平语
    default_lang: 中文
    source_code: zh
    target_code: ko
  2:  # 模式2：中韩敬语
    source_lang: 中文
    target_lang: 韩文
    style: 敬语
    default_lang: 中文
    source_code: zh
    target_code: ko
  3:  # 模式3：中日敬语
    source_lang: 中文
    target_lang: 日文
    style: 敬语
    default_lang: 中文
    source_code: zh
    target_code: ja
  4:  # 模式4：中日平语
    source_lang: 中文
    target_lang: 日文
    style: 平语
    default_lang: 中文
    source_code: zh
    target_code: ja
  5:  # 模式5：中英
    source_lang: 中文
    target_lang: 英文
    style: ""
    default_lang: 中文
    source_code: zh
    target_code: en

# 语言特征配置
language_features:
  zh:  # 中文特征
    pattern: "[\\u4E00-\\u9FFF]"  # 汉字Unicode范围
    exclusive:  # 排除特征
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 汉字  # 描述
  ko:  # 韩文特征
    pattern: "[\\uAC00-\\uD7AF]"  # 韩文谚文Unicode范围
    exclusive: 
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 韩文谚文  # 描述
  ja:  # 日文特征
    pattern: "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 日文假名Unicode范围
    exclusive:
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
    desc: 日文假名  # 描述
  en:  # 英文特征
    pattern: "[A-Za-z]"  # 英文字母范围
    exclusive:  # 排除特征
      - "[\\u4E00-\\u9FFF]"  # 排除中文
      - "[\\uAC00-\\uD7AF]"  # 排除韩文
      - "[\\u3040-\\u309F\\u30A0-\\u30FF]"  # 排除日文
    desc: 英文拉丁字母  # 描述

# 特殊语言组配置
special_language_groups: {}

# 特殊语言对配置
special_language_pairs: {}
'''

# 通用提示词模板
UNIVERSAL_PROMPT_TEMPLATE = """
你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 {input_lang} 和输出为 {output_lang} ，将内容从 {input_lang} 翻译成 {output_lang} {style_instruction}
- 若未指定语言或输入既非 {source_lang} 也非 {target_lang} ，则翻译为 {default_lang} 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 {output_lang}，调整语气和风格，考虑文化内涵和地区差异。不得包含 {input_lang} 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 {source_tone} 中的语气助词时，才将其翻译为 {output_lang} 中等效的 {target_tone} 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 {output_lang} 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 {output_lang} 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！
"""

def get_default_config() -> Dict:
    """获取默认配置字典"""
    return {
        "api_key": "",
        "model_id": "gemini-2.0-flash",
        "gemini_fallback_model_id": "gemini-pro",
        "openai_fallback_model_id": "gpt-3.5-turbo",
        "api_mode": "gemini",
        "api_base_url": "https://api.openai.com",
        "api_endpoint": "/v1/chat/completions",
        # 模型生成参数
        "temperature": 0.1,
        "top_p": 0.85,
        "max_output_tokens": 1024,
        "top_k": 64,
        "frequency_penalty": 0.0,
        "presence_penalty": 0.0,
        # 翻译行为配置
        "translation_mode": 1,
        "max_text_length": 500,
        "context_max_count": 8,
        "short_text_threshold": 10,
        "lang_detection_threshold": 0.9,
        "same_language_match_threshold": 0.5,  # 检测翻译结果与原文相似度的阈值
        # 网络和请求配置
        "tcp_connector": {
            "limit": 10,
            "ttl_dns_cache": 300,
            "keepalive_timeout": 60
        },
        "timeout": {
            "total": 30,
            "connect": 10,
            "sock_connect": 10,
            "sock_read": 20
        },
        "network_check": {
            "hosts": ["*******", "*******"],
            "port": 53,
            "timeout": 2
        },
        "api_health_check": {
            "timeout_total": 10,
            "timeout_connect": 5,
            "timeout_sock_connect": 5,
            "timeout_sock_read": 8,
            "test_prompt": "Hello, API check"
        },
        # 网络配置
        "request_min_interval": 1.0,
        # 日志和调试
        "debug_mode": False,
        "log_info_max": 50,
        "log_other_max": 50,
        # GUI配置
        "show_gui_progress": True,
        # 文本过滤配置
        "common_symbols": r'[,.!?;:"\'\'()\[\]\{\}<>+=*/&@#$%^&*~|_，。！？；：、""''（）【】《》]',
        "illegal_chars": r'[\x00-\x1F\x7F-\x9F]',
        # 安全设置
        "safety_settings": {
            "gemini": [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
        },
        # 语言相关新增配置
        "language_detection_cache_size": 100,
        "translation_cache_size": 50,
        "language_families": {
            "cjk": ["zh", "ja", "ko"],  # 中日韩语系
            "european": ["en", "fr", "de", "es", "it", "pt", "ru"],  # 欧洲语系
            "indic": ["hi", "bn", "ur"]  # 印度语系
        },
        "language_specific_settings": {},  # 每种语言的特定设置
        # 中韩文区分配置
        "ko_zh_detection": {
            "enabled": True,  # 是否启用增强的中韩文区分
            "ko_specific_ratio_threshold": 0.3,  # 韩文特有结构占比阈值
            "lang_feature_score_threshold": 0.3,  # 语言特征匹配分数阈值
            "feature_dominance_ratio": 2.0,  # 一种语言特征明显占优的比例
            "cjk_feature_score_threshold": 0.35  # CJK语言族内特征匹配分数阈值
        },
        # 通用语气符号配置
        "universal_punctuation": {
            "question_marks": {
                "universal": ["?", "？"],  # 所有语言通用的问号
                "latin": ["?"],            # 拉丁语系
                "cjk": ["？"],             # 中日韩
            },
            "exclamation_marks": {
                "universal": ["!", "！"],
                "latin": ["!"],
                "cjk": ["！"],
            }
        }
    }

def create_default_config():
    """创建默认配置文件"""
    # 构建默认配置文本，添加加密密钥说明
    config_text = DEFAULT_CONFIG_TEXT.replace(
        'api_key: ""  # 您的 API 密钥，必填，例如 "your-api-key-here"', 
        'api_key: ""  # 必须使用加密格式的API密钥，使用api_crypto.py工具加密'
    )
    
    # 使用二进制模式写入，避免空字符问题
    config_bytes = config_text.encode('utf-8')
    with open(CONFIG_FILE, "wb") as f:
        f.write(config_bytes)
    logger.info(f"已创建带注释的默认配置文件: {CONFIG_FILE}")

def load_config() -> Dict:
    """加载配置文件，若不存在则创建默认配置，并解密API密钥"""
    first_run = False # 初始化 first_run 为 False
    # 使用标准yaml库
    if not os.path.exists(CONFIG_FILE):
        create_default_config()
        logger.info("首次运行已创建默认配置文件，初次翻译可能有轻微延迟")
        first_run = True
        
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f) or {}
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}，重新创建默认配置")
        create_default_config()
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)

    # 确保所有必需键存在
    required_keys = [
        # API配置
        "api_key", "model_id", "gemini_fallback_model_id", "openai_fallback_model_id", "api_mode", "api_base_url", "api_endpoint",
        # 模型生成参数
        "temperature", "top_p", "max_output_tokens", "top_k", "frequency_penalty", "presence_penalty",
        # 翻译行为配置
        "translation_mode", "max_text_length", "context_max_count", "short_text_threshold", "lang_detection_threshold", "same_language_match_threshold",
        # 网络和请求配置
        "tcp_connector", "timeout", "network_check", "api_health_check",
        # 网络配置
        "request_min_interval",
        # 日志和调试
        "debug_mode", "log_info_max", "log_other_max",
        # GUI配置
        "show_gui_progress",
        # 文本过滤配置
        "common_symbols", "illegal_chars",
        # 安全设置
        "safety_settings"
    ]
    
    # 获取默认配置
    default_config = get_default_config()
    
    for key in required_keys:
        if key not in config:
            config[key] = default_config[key]
            
    # 移除废弃的重试相关配置项（如果存在）
    deprecated_keys = ["max_retries", "retry_base_delay", "api_request_attempts", "api_request_retry_delays"]
    for key in deprecated_keys:
        if key in config:
            logger.debug(f"从配置中移除废弃的字段: {key}")
            config.pop(key)

    # 只保留Config类定义中的字段，移除多余字段（如thinking_model）
    config_fields = set(f.name for f in Config.__dataclass_fields__.values())
    remove_keys = [k for k in config if k not in config_fields]
    for k in remove_keys:
        logger.debug(f"从配置中移除未在Config定义中的字段: {k}")
        config.pop(k)

    # 如果配置文件中没有API密钥或API密钥为空，提示用户输入
    if not config["api_key"] or first_run:
        encrypted_api_key = prompt_for_api_key()
        config["api_key"] = encrypted_api_key
        # 保存加密后的API密钥到配置文件
        save_config(config)
        logger.info("已将加密后的API密钥保存到配置文件")

    # API密钥解密处理
    global decrypted_api_key
    decrypted_api_key = get_real_api_key(config["api_key"])
    if not decrypted_api_key:
        logger.error("API密钥无效：必须使用加密格式的API密钥")
        sys.exit(1)
    
    # 配置日志级别
    logger.setLevel(logging.DEBUG if config["debug_mode"] else logging.INFO)
    console_handler.setLevel(logging.DEBUG if config["debug_mode"] else logging.INFO)
    logger.handlers = [console_handler]  # 只使用控制台处理器
    return config

def prompt_for_api_key() -> str:
    """提示用户输入加密后的API密钥"""
    logger.info("未在 config.yaml 中找到有效 API 密钥，请输入加密后的 API 密钥。")
    logger.info("注意：本程序只接受加密格式的API密钥，请使用api_crypto.py工具进行加密。")
    
    while True:
        api_key = input("请输入加密后的 API 密钥（输入后按回车）：").strip()
        if not api_key:
            logger.warning("API 密钥不能为空，请重新输入。")
            continue
            
        # 初始化API加密工具，检查输入的是否为有效的加密API密钥
        init_api_crypto()
        if api_crypto.is_encrypted(api_key):
            return api_key
        else:
            logger.error("输入的不是有效的加密API密钥，请使用api_crypto.py工具进行加密。")

@dataclass
class Config:
    api_key: str
    model_id: str
    gemini_fallback_model_id: str
    openai_fallback_model_id: str
    api_mode: str
    api_base_url: str
    api_endpoint: str
    # 模型生成参数
    temperature: float
    top_p: float
    max_output_tokens: int
    top_k: int
    frequency_penalty: float
    presence_penalty: float
    # 翻译行为配置
    translation_mode: int
    max_text_length: int
    context_max_count: int
    short_text_threshold: int
    lang_detection_threshold: float
    # 网络和请求配置
    tcp_connector: Dict
    timeout: Dict
    network_check: Dict
    # 网络配置
    request_min_interval: float
    # 日志和调试
    debug_mode: bool
    log_info_max: int
    log_other_max: int
    # GUI配置
    show_gui_progress: bool
    # 文本过滤配置
    common_symbols: str
    illegal_chars: str
    # 安全设置
    safety_settings: Dict
    # 语言相关新增配置
    language_detection_cache_size: int = 100  # 语言检测缓存大小
    translation_cache_size: int = 50  # 翻译结果缓存大小
    same_language_match_threshold: float = 0.5  # 检测翻译结果与原文相似度的阈值
    language_families: Dict = field(default_factory=lambda: {
        "cjk": ["zh", "ja", "ko"],
        "european": ["en", "fr", "de", "es", "it", "pt", "ru"],
        "indic": ["hi", "bn", "ur"]
    })  # 语言家族分组
    language_specific_settings: Dict = field(default_factory=dict)  # 每种语言的特定设置
    # 中韩文区分配置 - 新增
    ko_zh_detection: Dict = field(default_factory=lambda: {
        "enabled": True,  # 是否启用增强的中韩文区分
        "ko_specific_ratio_threshold": 0.3,  # 韩文特有结构占比阈值
        "lang_feature_score_threshold": 0.3,  # 语言特征匹配分数阈值
        "feature_dominance_ratio": 2.0,  # 一种语言特征明显占优的比例
        "cjk_feature_score_threshold": 0.35  # CJK语言族内特征匹配分数阈值
    })  # 中韩文区分配置
    # 通用语气符号配置
    universal_punctuation: Dict = field(default_factory=lambda: {
        "question_marks": {
            "universal": ["?", "？"],
            "latin": ["?"],
            "cjk": ["？"],
        },
        "exclamation_marks": {
            "universal": ["!", "！"],
            "latin": ["!"],
            "cjk": ["！"],
        }
    })  # 通用标点符号配置

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项的值，如果不存在则返回默认值
        
        Args:
            key: 配置项的键
            default: 默认值
            
        Returns:
            Any: 配置项的值或默认值
        """
        try:
            value = getattr(self, key, default)
            return value
        except Exception as e:
            logger.error(f"获取配置项 {key} 时出错: {e}")
            return default

def deep_merge_configs(base: Dict, update: Dict) -> Dict:
    """递归深度合并两个配置字典，保留嵌套结构
    
    Args:
        base: 基础配置字典（将被更新）
        update: 更新的配置字典（优先级更高）
        
    Returns:
        Dict: 合并后的配置字典
    """
    for key, value in update.items():
        # 如果是字典类型且在基础配置中也是字典，递归合并
        if isinstance(value, dict) and key in base and isinstance(base[key], dict):
            base[key] = deep_merge_configs(base[key], value)
        else:
            # 否则直接更新
            base[key] = value
    return base

def _to_yaml_compatible(data: Any) -> Any:
    """将数据转换为 ruamel.yaml 兼容的格式，特别是处理多行字符串。"""
    if isinstance(data, dict):
        return {k: _to_yaml_compatible(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [_to_yaml_compatible(item) for item in data]
    elif isinstance(data, str) and "\\n" in data: # 假设包含 \\n 的是多行字符串
        return PreservedScalarString(data.replace("\\n", "\\n"))
    return data

def _merge_ruamel_data(target: Any, source: Any):
    """
    递归合并 'source' 到 'target'。
    'target' 是 ruamel.yaml 加载的数据 (CommentedMap, CommentedSeq)。
    'source' 是普通的 Python dict/list。
    """
    if isinstance(source, dict):
        if not isinstance(target, dict): # 如果目标不是字典，则无法合并，直接替换
            return _to_yaml_compatible(source)
        for key, value in source.items():
            if key in target:
                target[key] = _merge_ruamel_data(target[key], value)
            else:
                target[key] = _to_yaml_compatible(value)
        return target
    elif isinstance(source, list):
        # 对于列表，通常是直接替换，或者需要更复杂的合并逻辑（这里简化为替换）
        return _to_yaml_compatible(source)
    else:
        return _to_yaml_compatible(source)

def save_config(config: Union[Dict, Config], filename=CONFIG_FILE) -> bool: # 修正类型提示
    """保存配置文件，保留注释和格式"""
    config_dict = config if isinstance(config, dict) else asdict(config)
    
    yaml_loader = YAML()
    yaml_loader.preserve_quotes = True
    yaml_loader.indent(mapping=2, sequence=4, offset=2)

    try:
        data_to_save = None
        if os.path.exists(filename) and os.path.getsize(filename) > 0:
            try:
                with open(filename, "r", encoding="utf-8") as f:
                    data_to_save = yaml_loader.load(f)
                if not isinstance(data_to_save, dict): # 确保加载的是字典
                    logger.warning(f"配置文件 {filename} 内容格式不正确，将使用默认配置覆盖。")
                    data_to_save = None
            except Exception as e:
                logger.error(f"读取配置文件 {filename} 失败: {e}。将尝试使用默认配置。")
                data_to_save = None
        
        if data_to_save is None: # 文件不存在，为空，或加载失败
            logger.info(f"由于文件不存在、为空或加载失败，将从 DEFAULT_CONFIG_TEXT 创建配置: {filename}")
            try:
                # 使用 io.StringIO 包装字符串，使其可以被 ruamel.yaml.load 解析
                default_config_stream = io.StringIO(DEFAULT_CONFIG_TEXT)
                data_to_save = yaml_loader.load(default_config_stream)
                if not isinstance(data_to_save, dict): # 再次检查
                     logger.error(f"DEFAULT_CONFIG_TEXT 解析失败，无法创建带注释的默认配置。")
                     data_to_save = get_default_config() # 退回到普通字典
            except Exception as e_load_default:
                logger.error(f"从 DEFAULT_CONFIG_TEXT 加载默认配置失败: {e_load_default}。将使用普通字典。")
                data_to_save = get_default_config() # 退回到普通字典，可能丢失注释

        # 将新的配置值合并到加载的（可能带有注释的）数据中
        # 如果 data_to_save 仍然不是 ruamel 加载的 CommentedMap，则直接使用 config_dict
        if isinstance(data_to_save, dict): # 包括 CommentedMap
            _merge_ruamel_data(data_to_save, config_dict)
        else: # 如果加载失败或者默认配置就是普通字典
            data_to_save = config_dict


        with open(filename, "w", encoding="utf-8") as f:
            yaml_loader.dump(data_to_save, f)
        
        logger.info(f"配置文件已更新: {filename}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件 {filename} 失败: {e}", exc_info=True)
        # 尝试使用简单的 PyYAML 保存方法作为备份
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                yaml.dump(config_dict, file, allow_unicode=True, default_flow_style=False, sort_keys=False, indent=2)
            logger.info(f"配置已通过备用方法 (PyYAML) 保存到 {filename}")
            return True
        except Exception as e2:
            logger.error(f"备用保存方式 (PyYAML) 也失败: {e2}")
            return False

def load_mode_config() -> Dict:
    """加载语言模式配置"""
    if not os.path.exists(MODE_CONFIG_FILE):
        with open(MODE_CONFIG_FILE, "w", encoding="utf-8") as f:
            f.write(DEFAULT_MODE_CONFIG_TEXT)

    try:
        with open(MODE_CONFIG_FILE, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载语言模式配置文件失败: {e}")
        with open(MODE_CONFIG_FILE, "wb") as f:
            f.write(DEFAULT_MODE_CONFIG_TEXT.encode('utf-8'))
        with open(MODE_CONFIG_FILE, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f)
        return config

def get_language_mode_config() -> Dict:
    """获取语言模式配置 (兼容性函数，与load_mode_config功能相同)"""
    config = load_mode_config()
    # 确保完成语言特性和语气的扩展配置
    config = complete_language_features_and_tones(config)
    # 保存更新后的配置
    save_mode_config(config)
    return config

def complete_language_features_and_tones(mode_config: Dict) -> Dict:
    """补全语言特征和语气词
    
    尝试补全配置中缺失的语言特征和语气词定义，为未来扩展到更多语言提供支持。
    
    Args:
        mode_config: 模式配置字典
        
    Returns:
        Dict: 补全后的模式配置
    """
    # 确保基础键存在
    required_keys = ['tone_particles', 'language_features', 'special_language_groups', 'special_language_pairs']
    for key in required_keys:
        if key not in mode_config:
            mode_config[key] = {}
            logger.debug(f"在mode_config中创建缺失的键: {key}")
    
    # 收集所有需要支持的语言代码
    required_langs = set()
    for mode in mode_config['translation_modes'].values():
        source_code = mode.get('source_code')
        target_code = mode.get('target_code')
        if source_code:
            required_langs.add(source_code)
        if target_code:
            required_langs.add(target_code)
            
    logger.debug(f"需要支持的语言代码: {required_langs}")
    
    # 默认语言特征配置 - 只包括常见语言
    default_language_features = {
        "zh": {
            "pattern": r"[\u4E00-\u9FFF]",  # 中文汉字
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "中文字符",
            "question_pattern": r"[?？]",
            "exclamation_pattern": r"[!！]"
        },
        "ko": {
            "pattern": r"[\uAC00-\uD7AF]",  # 韩文谚文
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "韩文字符",
            "question_pattern": r"[?？]",
            "exclamation_pattern": r"[!！]",
            "unique_features": ["요", "니다", "이다", "음", "군요", "네요", "아요", "어요", "ㅋㅋ", "ㅎㅎ"]  # 韩文特有词尾和语气词
        },
        "ja": {
            "pattern": r"[\u3040-\u309F\u30A0-\u30FF\u31F0-\u31FF]",  # 日文假名（平假名+片假名）
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "日文字符",
            "question_pattern": r"[?？]",
            "exclamation_pattern": r"[!！]"
        },
        "en": {
            "pattern": r"[a-zA-Z]",  # 英文字母
            "exclusive": [],  # 没有排他性特征，避免误判
            "desc": "英文字符",
            "question_pattern": r"[?]",
            "exclamation_pattern": r"[!]"
        }
    }
    
    # 默认语气词配置
    default_tone_particles = {
        "zh": "[哈哈|嘿嘿|呵呵|啦|呀|呀|哟|哦]",  # 中文语气词
        "ko": "[ㅋ|ㅎ|아|네|헤]",  # 韩文语气词
        "ja": "[ｗ笑]+",  # 日文语气词
        "en": "[lol|haha|hehe|yeah]"  # 英文语气词
    }
    
    # 为每个所需语言补充语言特征
    existing_langs = set(mode_config['language_features'].keys())
    
    for lang in required_langs:
        # 1. 补充语言特征
        if lang not in existing_langs:
            # 使用默认特征配置（如果有）
            if lang in default_language_features:
                mode_config['language_features'][lang] = default_language_features[lang].copy()
                logger.debug(f"为语言 {lang} 添加默认语言特征配置")
            else:
                # 为新语言添加一个通用的语言特征模式
                mode_config['language_features'][lang] = {
                    "pattern": r"[\w\p{L}]",  # 使用通用的字母和Unicode字符类
                    "exclusive": [],
                    "desc": f"{lang}语言字符",
                    "question_pattern": r"[?]",
                    "exclamation_pattern": r"[!]"
                }
                logger.debug(f"为语言 {lang} 添加通用语言特征配置")
        else:
            # 如果已存在但配置不完整，补充默认值
            feature = mode_config['language_features'][lang]
            if "pattern" not in feature:
                if lang in default_language_features:
                    feature["pattern"] = default_language_features[lang]["pattern"]
                else:
                    feature["pattern"] = r"[\w\p{L}]"  # 通用模式
                
            if "exclusive" not in feature:
                feature["exclusive"] = []
                
            if "desc" not in feature:
                if lang in default_language_features:
                    feature["desc"] = default_language_features[lang]["desc"]
                else:
                    feature["desc"] = f"{lang}语言字符"
                    
            # 确保有问号和感叹号模式
            if "question_pattern" not in feature:
                if lang in default_language_features:
                    feature["question_pattern"] = default_language_features[lang]["question_pattern"]
                else:
                    feature["question_pattern"] = r"[?]"
                    
            if "exclamation_pattern" not in feature:
                if lang in default_language_features:
                    feature["exclamation_pattern"] = default_language_features[lang]["exclamation_pattern"]
                else:
                    feature["exclamation_pattern"] = r"[!]"
    
        # 2. 补充语气词配置
        if lang not in mode_config['tone_particles']:
            # 使用默认配置，如果有的话
            if lang in default_tone_particles:
                mode_config['tone_particles'][lang] = default_tone_particles[lang]
                logger.debug(f"为语言 {lang} 添加默认语气词配置")
            else:
                # 对于未知语言，使用通用模式
                mode_config['tone_particles'][lang] = r"[\w\p{P}]+"
                logger.debug(f"为未知语言 {lang} 添加通用语气词配置")
    
    # 添加或更新语言组配置
    default_language_groups = {
        "cjk": {
            "languages": ["zh", "ja", "ko"],
            "strict_detection": False,  # 改为False，避免严格检测引起的误判
            "desc": "中日韩语言组"
        },
        "latin": {
            "languages": ["en", "fr", "es", "de", "it", "pt", "nl", "sv", "da", "no", "fi"],
            "strict_detection": False,
            "desc": "拉丁语系"
        },
        "slavic": {
            "languages": ["ru", "uk", "be", "bg", "pl", "cs", "sk", "sl", "hr", "bs", "sr", "mk"],
            "strict_detection": False,
            "desc": "斯拉夫语系"
        },
        "indic": {
            "languages": ["hi", "bn", "pa", "gu", "mr", "ta", "te", "kn", "ml"],
            "strict_detection": False,
            "desc": "印度语系"
        },
        "semitic": {
            "languages": ["ar", "he", "mt", "am", "ti"],
            "strict_detection": False,
            "desc": "闪米特语系"
        }
    }
    
    # 更新语言组配置
    for group_name, group_config in default_language_groups.items():
        if group_name not in mode_config['special_language_groups']:
            mode_config['special_language_groups'][group_name] = group_config
            logger.debug(f"添加语言组 {group_name}")
        else:
            # 更新现有语言组中的languages列表，合并而不是替换
            existing_group = mode_config['special_language_groups'][group_name]
            if "languages" in existing_group:
                existing_languages = set(existing_group["languages"])
                new_languages = set(group_config.get("languages", []))
                # 合并语言列表
                combined_languages = list(existing_languages.union(new_languages))
                existing_group["languages"] = combined_languages
                # 确保其他键有默认值
                if "strict_detection" not in existing_group:
                    existing_group["strict_detection"] = group_config.get("strict_detection", False)
                if "desc" not in existing_group:
                    existing_group["desc"] = group_config.get("desc", f"{group_name}语言组")
    
    # 添加通配符语言对 - 适用于所有语言
    if "*-*" not in mode_config['special_language_pairs']:
        mode_config['special_language_pairs']["*-*"] = {
            "max_attempts": 2,
            "desc": "通用语言对配置",
            "skip_source_detection": False,  # 默认不跳过源语言检测
            "min_char_ratio": 0.2           # 默认最小字符比例
        }
    
    # 处理特殊语言对配置
    # 确保不同语言组之间和组内的语言对都有特殊处理配置
    all_langs = set()
    for group_info in mode_config['special_language_groups'].values():
        all_langs.update(group_info.get("languages", []))
    
    # 为每个已定义的语言对添加基本配置
    for mode_id, mode_info in mode_config['translation_modes'].items():
        source = mode_info.get("source_code")
        target = mode_info.get("target_code")
        
        if source and target and source != target:
            pair_key = f"{source}-{target}"
            if pair_key not in mode_config['special_language_pairs']:
                # 判断源语言和目标语言所属的语言组
                source_in_cjk = source in mode_config['special_language_groups'].get('cjk', {}).get('languages', [])
                target_in_cjk = target in mode_config['special_language_groups'].get('cjk', {}).get('languages', [])
                
                # 为不同类型的语言对设置不同的配置
                if source_in_cjk and target_in_cjk:
                    # CJK语言之间的互译
                    mode_config['special_language_pairs'][pair_key] = {
                        "max_attempts": 2,
                        "desc": f"{source}-{target}互译配置",
                        "skip_source_detection": True,  # CJK互译特殊处理
                        "min_char_ratio": 0.3
                    }
                elif source_in_cjk or target_in_cjk:
                    # CJK与其他语系的互译
                    mode_config['special_language_pairs'][pair_key] = {
                        "max_attempts": 2,
                        "desc": f"{source}-{target}互译配置",
                        "skip_source_detection": False,  # 需检测源语言
                        "min_char_ratio": 0.15,
                        "allow_short_text_mismatch": True  # 对短文本宽松处理
                    }
                else:
                    # 其他语言对
                    mode_config['special_language_pairs'][pair_key] = {
                        "max_attempts": 1,
                        "desc": f"{source}-{target}互译配置",
                        "skip_source_detection": False,
                        "min_char_ratio": 0.1
                    }
                    
                logger.debug(f"为语言对 {pair_key} 添加默认配置")
    
    # 为所有现有语言组之间的互译创建基本配置
    groups = list(mode_config['special_language_groups'].keys())
    for source_group in groups:
        for target_group in groups:
            if source_group != target_group:
                group_pair = f"{source_group}-{target_group}"
                if group_pair not in mode_config['special_language_pairs']:
                    # 为不同类型的语言组对设置不同的配置
                    if source_group == "cjk" or target_group == "cjk":
                        # CJK与其他语组的互译
                        mode_config['special_language_pairs'][group_pair] = {
                            "desc": f"{source_group}与{target_group}语言组互译配置",
                            "min_char_ratio": 0.15,
                            "allow_short_text_mismatch": True  # 对短文本宽松处理
                        }
                    else:
                        # 其他语言组对
                        mode_config['special_language_pairs'][group_pair] = {
                            "desc": f"{source_group}与{target_group}语言组互译配置",
                            "min_char_ratio": 0.1
                        }
    
    # 添加语言组内部配置
    for group_name, group_info in mode_config['special_language_groups'].items():
        group_languages = group_info.get("languages", [])
        
        # 为组内语言添加通配符配置
        wildcard_key = f"{group_name}-*"
        reverse_wildcard_key = f"*-{group_name}"
        
        if wildcard_key not in mode_config['special_language_pairs']:
            mode_config['special_language_pairs'][wildcard_key] = {
                "desc": f"{group_name}语言组对外互译",
                "min_char_ratio": 0.15,
                "max_attempts": 2
            }
            
        if reverse_wildcard_key not in mode_config['special_language_pairs']:
            mode_config['special_language_pairs'][reverse_wildcard_key] = {
                "desc": f"外部语言翻译到{group_name}语言组",
                "min_char_ratio": 0.15,
                "max_attempts": 2
            }
    
    return mode_config

def save_mode_config(mode_config: Dict, filename=MODE_CONFIG_FILE): # 添加 filename 参数
    """保存模式配置到文件，保留注释和格式"""
    yaml_loader = YAML()
    yaml_loader.preserve_quotes = True
    yaml_loader.indent(mapping=2, sequence=4, offset=2)

    try:
        data_to_save = None
        if os.path.exists(filename) and os.path.getsize(filename) > 0:
            try:
                with open(filename, "r", encoding="utf-8") as f:
                    data_to_save = yaml_loader.load(f)
                if not isinstance(data_to_save, dict):
                    logger.warning(f"模式配置文件 {filename} 内容格式不正确，将使用默认配置覆盖。")
                    data_to_save = None
            except Exception as e:
                logger.error(f"读取模式配置文件 {filename} 失败: {e}。将尝试使用默认配置。")
                data_to_save = None

        if data_to_save is None: # 文件不存在，为空，或加载失败
            logger.info(f"由于文件不存在、为空或加载失败，将从 DEFAULT_MODE_CONFIG_TEXT 创建模式配置: {filename}")
            try:
                default_mode_config_stream = io.StringIO(DEFAULT_MODE_CONFIG_TEXT)
                data_to_save = yaml_loader.load(default_mode_config_stream)
                if not isinstance(data_to_save, dict):
                     logger.error(f"DEFAULT_MODE_CONFIG_TEXT 解析失败，无法创建带注释的默认模式配置。")
                     # 尝试从 DEFAULT_MODE_CONFIG_TEXT 用 PyYAML 加载作为备选
                     data_to_save = yaml.safe_load(DEFAULT_MODE_CONFIG_TEXT) or {}
            except Exception as e_load_default:
                logger.error(f"从 DEFAULT_MODE_CONFIG_TEXT 加载默认模式配置失败: {e_load_default}。将使用普通字典。")
                data_to_save = yaml.safe_load(DEFAULT_MODE_CONFIG_TEXT) or {}


        # 合并传入的 mode_config 到 data_to_save
        if isinstance(data_to_save, dict): # 包括 CommentedMap
             _merge_ruamel_data(data_to_save, mode_config)
        else: # 如果加载失败或者默认配置就是普通字典
            data_to_save = mode_config


        with open(filename, "w", encoding="utf-8") as f:
            yaml_loader.dump(data_to_save, f)
        
        logger.info(f"模式配置文件已保存到 {filename}")

    except Exception as e:
        logger.error(f"保存模式配置文件 {filename} 时出错: {e}", exc_info=True)
        # 尝试使用简单的 PyYAML 保存方法作为备份
        try:
            with open(filename, 'w', encoding='utf-8') as file:
                yaml.dump(mode_config, file, allow_unicode=True, default_flow_style=False, sort_keys=False, indent=2)
            logger.info(f"模式配置已通过备用方法 (PyYAML) 保存到 {filename}")
        except Exception as e2:
            logger.error(f"备用保存方式 (PyYAML) 也失败: {e2}")

# 日志配置
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 添加网络和API状态管理类，实现缓存机制
class ServiceManager:
    """服务管理类，管理网络连接和API服务状态，提供缓存机制"""
    
    def __init__(self, config):
        self.config = config
        self.network_status = {
            "connected": None,  # 是否连接
            "last_check": 0,    # 上次检查时间戳
            "check_interval": 5  # 默认缓存有效期（秒）
        }
        self.api_status = {
            "healthy": None,      # API是否健康
            "message": "",        # 状态消息
            "last_check": 0,      # 上次检查时间戳 
            "check_interval": 300  # 默认缓存有效期（秒）
        }
        
    def is_network_connected(self, force_check=False) -> bool:
        """检查网络连接状态，带缓存机制
    
    Args:
            force_check: 是否强制检查而忽略缓存

    Returns:
            网络是否连接的布尔值
        """
        current_time = time.time()
        
        # 如果缓存有效且不是强制检查，直接返回缓存的结果
        if (not force_check and 
            self.network_status["connected"] is not None and 
            current_time - self.network_status["last_check"] < self.network_status["check_interval"]):
            logger.debug(f"使用缓存的网络状态: {'已连接' if self.network_status['connected'] else '未连接'}")
            return self.network_status["connected"]
            
        # 执行实际的网络检查
        is_connected = self._check_network_impl()
        
        # 更新缓存
        self.network_status["connected"] = is_connected
        self.network_status["last_check"] = current_time
        
        return is_connected
        
    def _check_network_impl(self) -> bool:
        """实际执行网络连接检查的内部方法"""
        network_check = self.config.network_check
        hosts = network_check.get("hosts", ["*******", "*******"])
        port = network_check.get("port", 53)
        timeout = network_check.get("timeout", 2)
        
        # 初始化远程主机和端口
        remote_hosts = hosts if isinstance(hosts, list) else [hosts]
        
        # 尝试连接每一个远程主机
        for host in remote_hosts:
            try:
                # 尝试建立TCP连接
                socket.create_connection((host, port), timeout=timeout)
                logger.debug(f"网络连接正常 ({host})")
                return True
            except OSError as e:
                logger.debug(f"无法连接到 {host}:{port}: {e}")
                continue
        
        # 所有连接尝试都失败
        logger.warning("网络连接检查失败，无法连接到任何远程主机")
        return False

    async def is_api_healthy(self, api_mode, api_key, model_id=None, api_base_url=None, force_check=False) -> tuple[bool, str]:
        """检查API服务健康状态，带缓存机制
    
    Args:
            api_mode: API模式
            api_key: API密钥
            model_id: 模型ID
            api_base_url: API基础URL
            force_check: 是否强制检查而忽略缓存
        
    Returns:
            (是否健康的布尔值, 状态消息)
        """
        current_time = time.time()
        
        # 如果缓存有效且不是强制检查，直接返回缓存的结果
        if (not force_check and 
            self.api_status["healthy"] is not None and 
            current_time - self.api_status["last_check"] < self.api_status["check_interval"]):
            logger.debug(f"使用缓存的API状态: {'健康' if self.api_status['healthy'] else '不健康'} - {self.api_status['message']}")
            return self.api_status["healthy"], self.api_status["message"]
            
        # 先检查网络连接
        if not self.is_network_connected():
            self.api_status["healthy"] = False
            self.api_status["message"] = "网络连接不可用"
            self.api_status["last_check"] = current_time
            return False, "网络连接不可用"
            
        # 执行API健康检查 - 使用全局函数
        healthy, message = await check_api_health(api_mode, api_key, model_id, api_base_url)
        
        # 更新缓存
        self.api_status["healthy"] = healthy
        self.api_status["message"] = message
        self.api_status["last_check"] = current_time
        
        return healthy, message
        
    def reset_network_cache(self):
        """重置网络状态缓存"""
        self.network_status["connected"] = None
        self.network_status["last_check"] = 0
        
    def reset_api_cache(self):
        """重置API状态缓存"""
        self.api_status["healthy"] = None
        self.api_status["last_check"] = 0

async def check_api_health(api_mode, api_key, model_id=None, api_base_url=None) -> tuple[bool, str]:
    """检查API健康状态
    
    检查API密钥有效性和服务可用性
    
    Args:
        api_mode: API模式 ("gemini" 或 "openai")
        api_key: API密钥
        model_id: 可选的模型ID，不提供时使用默认值
        api_base_url: OpenAI兼容API的基础URL
        
    Returns:
        元组 (健康状态布尔值, 状态消息)
    """
    try:
        test_prompt = "Hello"
        
        # 创建超时设置
        timeout = aiohttp.ClientTimeout(total=10, connect=5, sock_connect=5, sock_read=8)
        
        if api_mode == "gemini":
            try:
                import google.generativeai as genai
                genai.configure(api_key=api_key)
                # 尝试列出模型，验证API密钥
                models = genai.list_models()
                if models:
                    return True, "Gemini API 连接正常"
                else:
                    return False, "Gemini API 无法获取模型列表"
            except Exception as e:
                return False, f"Gemini API 测试失败: {str(e)}"
        
        elif api_mode == "openai":
            # 使用 aiohttp 测试 OpenAI 兼容 API
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {api_key.strip()}"
            }
            
            # 如果未提供基础URL，使用默认值
            base_url = api_base_url or "https://api.openai.com"
            
            # 构建URL - 确保正确处理API端点路径
            api_endpoint = "/v1/models"
            if not api_endpoint.startswith('/'):
                api_endpoint = '/' + api_endpoint
            
            url = f"{base_url.rstrip('/')}{api_endpoint}"
            
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(url, headers=headers) as response:
                        if response.status == 200:
                            return True, "OpenAI API 连接正常"
                        else:
                            error_text = await response.text()
                            return False, f"OpenAI API 返回错误: {response.status} - {error_text[:100]}"
            except Exception as e:
                return False, f"OpenAI API检查过程中出错: {str(e)}"
        else:
            return False, f"不支持的API模式: {api_mode}"
    except Exception as e:
        return False, f"API健康检查过程中出现未知错误: {str(e)}"



# --- 辅助函数：获取语言族 ---
def get_language_family(lang_code: str, language_families: Dict) -> Optional[str]:
    """根据语言代码查找所属的语言族"""
    if not language_families:
        return None
    for family, langs in language_families.items():
        if lang_code in langs:
            return family
    return None

# --- 辅助函数：特定语族歧义解决规则 (示例) ---
def resolve_cjk_ambiguity(lang1: str, lang2: str, lang_features: Dict, text: str, config: Config) -> Optional[str]: # 明确使用 Config 类型
    """
    解决 CJK 语族内部歧义的规则函数 (示例：中韩文区分)。
    config 应包含类似原 ko_zh_detection 的参数。
    返回更可能的语言代码，或 None 表示无法明确解决。
    """
    # 从传入的 config 对象安全地获取 ko_zh_detection 配置
    ko_zh_config = getattr(config, 'ko_zh_detection', {}) 
    enabled = ko_zh_config.get("enabled", False)
    
    if not enabled or not ("ko" in [lang1, lang2] and "zh" in [lang1, lang2]):
        return None # 如果禁用或不是中韩文歧义，则不处理

    ko_score = lang_features.get("ko", {}).get("match_score", 0)
    zh_score = lang_features.get("zh", {}).get("match_score", 0)
    dominance_ratio = ko_zh_config.get("feature_dominance_ratio", 2.0)
    ko_specific_threshold = ko_zh_config.get("ko_specific_ratio_threshold", 0.3)
    
    logger.debug(f"运行CJK歧义规则(中韩): zh_score={zh_score:.4f}, ko_score={ko_score:.4f}")

    if zh_score > ko_score * dominance_ratio:
        logger.debug(f"CJK规则：中文特征明显占优")
        return "zh"
    if ko_score > zh_score * dominance_ratio:
        logger.debug(f"CJK规则：韩文特征明显占优")
        return "ko"
        
    # 检查韩文特有结构 (示例逻辑，需要完善)
    korean_specific_pattern = r"[\uAC00-\uD7A3]+"
    try:
        ko_specific_matches = regex.findall(korean_specific_pattern, text)
        if ko_specific_matches and len(text) > 0:
            ko_specific_ratio = sum(len(m) for m in ko_specific_matches) / len(text)
            logger.debug(f"韩文特有结构占比: {ko_specific_ratio:.4f}")
            if ko_specific_ratio > ko_specific_threshold:
                logger.debug(f"CJK规则：根据韩文特有结构判定为韩文")
                return "ko"
    except regex.error as e:
        logger.error(f"CJK规则中韩文特有结构正则错误: {e}")
            
    # ... (可以加入更多判断逻辑，如韩文粒子检查等) ...

    # 如果规则无法明确区分，返回 None
    logger.debug("CJK规则：未能明确区分中韩文")
    return None


# --- 新的通用决策逻辑函数 ---
def run_universal_decision_logic(
    main_detector_results: List[Dict], 
    feature_results: Dict, 
    is_short_text: bool, 
    main_detector_threshold: float, 
    language_families: Dict, 
    specific_family_rules: Dict, 
    supported_langs: Dict,
    sample_text: str, 
    config: Config,
    hint_lang: Optional[str] = None # 新增：提示语言参数
) -> str:
    """
    通用的决策逻辑，结合主检测器 (pycld2) 和特征匹配结果。

    Args:
        main_detector_results: 来自 pycld2 的候选列表 [{"lang": code, "prob": 0-1 probability}, ...]
        feature_results: 预计算的特征匹配结果 {lang_code: {"matches": bool, "excludes_violated": bool, "match_score": float, "desc": str}}
        is_short_text: 是否为短文本
        main_detector_threshold: 主检测器置信度基础阈值 (用于判断是否可靠)
        language_families: 语族配置
        specific_family_rules: 特定语族的细化规则函数字典 { "family_name": function(lang1, lang2, features, text, config) }
        supported_langs: 支持的语言字典 (用于检查语言是否有效)
        sample_text: 用于特定语族规则分析的原始文本片段
        config: 主配置对象，传递给语族规则函数使用
        hint_lang: 可选的提示语言，如果提供，会给予该语言更高的初始分数。

    Returns:
        最终判定的语言代码 (str) 或 "unknown"
    """
    combined_scores = {}
    
    # 如果有提示语言，并且该语言在支持的语言中，给予一个较高的初始偏置分数
    hint_bias = 0.2 # 可以调整这个偏置值
    if hint_lang and hint_lang in supported_langs and hint_lang in feature_results and not feature_results[hint_lang]["excludes_violated"]:
        combined_scores[hint_lang] = hint_bias
        logger.debug(f"决策逻辑：应用提示语言 '{hint_lang}' 偏置: +{hint_bias}")

    # 1. 结合主检测器和特征计算初始分数
    for candidate in main_detector_results:
        lang = candidate["lang"]
        prob = candidate["prob"] 
        
        if lang not in supported_langs or lang == 'un': 
            continue

        feature_info = feature_results.get(lang, {"matches": False, "excludes_violated": True, "match_score": 0})
        match_score = feature_info["match_score"]
        excludes_violated = feature_info["excludes_violated"]

        if excludes_violated:
            # 如果提示语言被排他规则排除，移除偏置
            if lang == hint_lang and lang in combined_scores:
                logger.debug(f"决策逻辑：提示语言 '{lang}' 因违反排他规则被排除，移除偏置")
                combined_scores.pop(lang) # 或者设置为负数标记排除
            else:
                # combined_scores[lang] = -1 # 标记明确排除，但下方已有移除逻辑
                pass # 后面会处理
            logger.debug(f"语言 {lang} 因违反排他规则被评估为低优先级或排除")
            continue 
            
        prob_weight = 0.7
        feature_weight = 0.3
        if is_short_text:
            prob_weight = 0.4
            feature_weight = 0.6

        # 基础分数计算
        current_score_base = (prob * prob_weight) + (match_score * feature_weight)
        
        # 如果当前语言是提示语言，确保其分数不低于基础分和偏置（如果未被排除）
        if lang == hint_lang and lang in combined_scores:
            combined_scores[lang] = max(combined_scores[lang], current_score_base + hint_bias) 
        elif lang == hint_lang: # 提示语言但之前未加入（例如cld2未检出但特征匹配）
            combined_scores[lang] = current_score_base + hint_bias
        else: # 非提示语言
            combined_scores[lang] = max(combined_scores.get(lang, 0), current_score_base)

    # 2. 基于特征匹配补充候选 (主要针对那些 cld2 可能漏掉但特征明显的语言)
    for lang, feature_info in feature_results.items():
        if lang not in supported_langs or feature_info["excludes_violated"]:
            continue
        
        # 如果该语言仅靠特征匹配，且未在cld2结果中，给一个基础分
        if lang not in combined_scores and feature_info["matches"] and feature_info["match_score"] > 0.1:
            base_feature_score = feature_info["match_score"] * 0.5 # 特征匹配的权重可以低一些
            if lang == hint_lang: # 如果这个纯特征匹配的是提示语言
                combined_scores[lang] = base_feature_score + hint_bias
                logger.debug(f"基于特征和提示补充候选: {lang} (score: {combined_scores[lang]:.4f})")
            else:
                combined_scores[lang] = base_feature_score
                logger.debug(f"基于特征补充候选: {lang} (score: {combined_scores[lang]:.4f})")
        # 如果已在combined_scores中 (可能来自cld2或hint)，但特征分更高，可以考虑更新，但要小心
        # 暂时不处理这种情况，避免过于复杂的分数调整

    # 移除排他规则排除的语言，并确保分数非负
    valid_scores = {}
    for lang, score in combined_scores.items():
        if feature_results.get(lang, {}).get("excludes_violated", False):
            logger.debug(f"决策逻辑：语言 {lang} 因确认违反排他规则最终被排除")
            continue
        valid_scores[lang] = max(0, score)
    combined_scores = valid_scores

    if not combined_scores:
        logger.warning("决策逻辑：没有可靠的语言候选")
        return "unknown"

    # 3. 排序和选择最佳候选
    # 排序时，主要看分数，如果分数相同，cld2的原始概率可以作为次要排序依据
    sorted_langs = sorted(
        combined_scores.items(), 
        key=lambda item: (item[1], next((c['prob'] for c in main_detector_results if c['lang'] == item[0]), 0)), 
        reverse=True
    )
    
    logger.debug(f"决策逻辑：综合评分排序 (含提示 '{hint_lang}'): {[(l, round(s,3)) for l,s in sorted_langs]}")
    best_lang, best_score = sorted_langs[0]

    # 4. 处理歧义 (逻辑保持，但现在已考虑提示语言)
    resolved_lang = None
    if len(sorted_langs) > 1:
        second_lang, second_score = sorted_langs[1]
        # 调整歧义判断阈值，如果最高分是hint_lang，可以适当放宽，允许它胜出
        ambiguity_factor = 1.4
        if best_lang == hint_lang:
            ambiguity_factor = 1.2 # 如果最佳是提示语言，它不需要领先太多
        
        if best_score < second_score * ambiguity_factor and best_score > 0.1: 
            logger.debug(f"决策逻辑：检测到歧义: {best_lang} ({best_score:.4f}) vs {second_lang} ({second_score:.4f})")
            best_family = get_language_family(best_lang, language_families)
            second_family = get_language_family(second_lang, language_families)
            if best_family and best_family == second_family and best_family in specific_family_rules:
                logger.debug(f"决策逻辑：歧义发生在同一语族 '{best_family}' 内，应用特定规则")
                rule_func = specific_family_rules[best_family]
                try:
                    resolved_lang = rule_func(best_lang, second_lang, feature_results, sample_text, config)
                    if resolved_lang:
                       logger.debug(f"决策逻辑：语族规则 '{best_family}' 解决了歧义，选择: {resolved_lang}")
                    else:
                       logger.debug(f"决策逻辑：语族规则 '{best_family}' 未能明确解决歧义")
                except Exception as e:
                    logger.error(f"执行语族 '{best_family}' 规则时出错: {e}")
            # 如果提示语言是其中一个，并且分数足够高，可能优先选择提示语言
            elif hint_lang and hint_lang in [best_lang, second_lang] and combined_scores.get(hint_lang, 0) > (second_score * 0.9 if hint_lang == best_lang else best_score * 0.9):
                 logger.debug(f"决策逻辑：歧义中包含提示语言 '{hint_lang}' 且其分数较高，倾向于选择提示语言")
                 resolved_lang = hint_lang
            else:
                 logger.debug(f"决策逻辑：歧义语言不在同一已配置规则的语族或无特定处理，保留最高分")

    final_lang_candidate = resolved_lang if resolved_lang else best_lang
    final_score = combined_scores.get(final_lang_candidate, 0)

    # 最终置信度阈值可以根据是否使用了hint进行调整
    final_confidence_threshold = 0.15 
    if hint_lang and final_lang_candidate == hint_lang:
        final_confidence_threshold = 0.1 # 如果最终是提示的语言，可以稍微降低门槛
        
    if final_score < final_confidence_threshold:
        # 如果是因为hint_lang导致分数低但仍被选中，再检查一下没有hint的情况会怎样
        if hint_lang and final_lang_candidate == hint_lang and best_lang != hint_lang and combined_scores.get(best_lang,0) >= 0.15:
            logger.warning(f"决策逻辑：提示语言 {hint_lang} 分数过低 ({final_score:.4f})，但存在其他更高置信度候选 {best_lang} ({combined_scores.get(best_lang,0):.4f})，选择后者")
            final_lang_candidate = best_lang
        else:
            logger.warning(f"决策逻辑：最终最佳语言 {final_lang_candidate} 的置信度 ({final_score:.4f}) 过低 (阈值 {final_confidence_threshold:.2f})，判定为 unknown")
            return "unknown"
        
    logger.info(f"决策逻辑：最终判定语言: {final_lang_candidate} (综合分数: {final_score:.4f}, 提示: {hint_lang})")
    return final_lang_candidate


# --- 主检测函数修改版 (替换旧的 detect_language_improved) ---
def detect_language_improved(
    text: str, 
    supported_langs: Dict[str, Dict], 
    language_features: Dict, 
    threshold: float, # pycld2 阈值 (0-100)
    short_text_threshold: int, 
    is_original: bool = True, 
    language_families: Dict = None, 
    specific_family_rules: Dict = None,
    config: Config = None # 传递 Config 对象
) -> str:
    """
    改进的语言检测函数 (使用 pycld2 和通用决策逻辑)。
    
    Args:
        text: 要检测的文本
        supported_langs: 支持的语言字典 { "code": {"name": "...", ...} }
        language_features: 语言特征字典 { "code": {"pattern": regex, "exclusive": [regex], "desc": str} }
        threshold: pycld2 置信度基础阈值 (0-100)
        short_text_threshold: 短文本判定阈值
        is_original: 是否为原文 (可能影响缓存或未来逻辑)
        language_families: 语言族配置 { "family": ["lang1", "lang2"] }
        specific_family_rules: 特定语族歧义解决规则 { "family": function }
        config: 主配置对象，用于传递给决策逻辑和语族规则

    Returns:
        检测到的语言代码或 "unknown"
    """
    global language_detection_cache # 假设缓存存在
    
    if specific_family_rules is None:
        specific_family_rules = {}
    if language_families is None:
         language_families = {}

    sample_text = text.strip()
    if not sample_text:
        return "unknown"
    
    # 缓存键 (保持不变或根据需要调整)
    cache_key = f"{sample_text[:100]}|{threshold}|{short_text_threshold}|{is_original}"
    if language_detection_cache is not None:
        cached_result = language_detection_cache.get(cache_key)
        if cached_result:
            logger.info(f"【语言检测】使用缓存结果: {cached_result}")
            return cached_result

    # 1. 执行特征匹配 (预计算所有支持语言的特征信息)
    feature_results = {}
    # 推荐在外部进行预编译以提高性能
    compiled_patterns = {} 
    for lang_code, feature in language_features.items():
        if lang_code not in supported_langs: 
             continue
        try:
            # 预编译主模式
            if lang_code not in compiled_patterns:
                 if "pattern" not in feature:
                      logger.warning(f"语言 {lang_code} 没有定义 pattern，跳过特征检测")
                      feature_results[lang_code] = {"matches": False, "excludes_violated": True, "match_score": 0, "desc": feature.get("desc", f"{lang_code} 语言")}
                      continue
                 try:
                      compiled_patterns[lang_code] = {"main": regex.compile(feature["pattern"]), "exclusive": []}
                 except regex.error as e:
                      logger.error(f"语言 {lang_code} 的主正则表达式错误: {e}")
                      feature_results[lang_code] = {"matches": False, "excludes_violated": True, "match_score": 0, "desc": feature.get("desc", f"{lang_code} 语言")}
                      continue

            # 预编译排他模式
            if "exclusive" in feature and isinstance(feature["exclusive"], list) and not compiled_patterns[lang_code]["exclusive"]:
                for i, excl_pattern_str in enumerate(feature["exclusive"]):
                    try:
                        compiled_patterns[lang_code]["exclusive"].append(regex.compile(excl_pattern_str))
                    except regex.error as e:
                        logger.error(f"语言 {lang_code} 的第 {i+1} 个排他性正则表达式错误: {e}")
                        # 可以选择仅忽略错误的排他模式，或将整个语言标记为错误
            
            pattern = compiled_patterns[lang_code]["main"]
            exclusive_patterns = compiled_patterns[lang_code]["exclusive"]
            
            matches = bool(pattern.search(sample_text))
            excludes_violated = any(excl.search(sample_text) for excl in exclusive_patterns)
            
            match_score = 0
            if matches and not excludes_violated and len(sample_text) > 0: # 仅在匹配且未被排除时计算分数
                try:
                    matched_content = regex.findall(pattern, sample_text)
                    match_len = sum(len(m) if isinstance(m, str) else 1 for m in matched_content)
                    match_score = min(match_len / len(sample_text), 1.0) # 确保不超过1
                except regex.error as e:
                     logger.error(f"计算语言 {lang_code} 的 match_score 时正则错误: {e}")

            feature_results[lang_code] = {
                "matches": matches, 
                "excludes_violated": excludes_violated,
                "desc": feature.get("desc", f"{lang_code} 语言"),
                "match_score": match_score
            }
        except Exception as e: # 捕捉更广泛的错误
            logger.error(f"处理语言 {lang_code} 的特征时发生意外错误: {e}")
            feature_results[lang_code] = {"matches": False, "excludes_violated": True, "match_score": 0, "desc": f"{lang_code} 语言"}
        # logger.debug(f"特征匹配 [{lang_code}]: score={feature_results[lang_code]['match_score']:.4f}, matches={feature_results[lang_code]['matches']}, excluded={feature_results[lang_code]['excludes_violated']}")

    # 2. 调用 pycld2 进行检测
    cld2_results = []
    try:
        is_reliable, text_bytes, detected_details = cld2.detect(sample_text)
        logger.debug(f"pycld2 检测结果: is_reliable={is_reliable}, details={detected_details}")
        
        for name, code, percent, score in detected_details:
            if code != 'un' and code in supported_langs:
                 cld2_results.append({"lang": code, "prob": percent / 100.0}) 

    except cld2.error as e:
        logger.error(f"pycld2 检测异常: {e}，将仅依赖特征匹配")
        cld2_results = [] 

    # 3. 调用通用决策逻辑
    is_short_text = len(sample_text) < short_text_threshold
    final_lang = run_universal_decision_logic(
        main_detector_results=cld2_results,
        feature_results=feature_results,
        is_short_text=is_short_text,
        main_detector_threshold=threshold / 100.0, # 将输入的阈值(0-100)转为0-1
        language_families=language_families if language_families else {}, # 确保是字典
        specific_family_rules=specific_family_rules if specific_family_rules else {}, # 确保是字典
        supported_langs=supported_langs,
        sample_text=sample_text, 
        config=config # 传递 Config 对象
    )

    # 4. 缓存结果
    if language_detection_cache is not None:
        # 只有当检测结果不是 unknown 时才缓存？或者总是缓存？当前是总是缓存
        language_detection_cache.put(cache_key, final_lang)
        logger.debug(f"【缓存更新】保存语言检测结果: {final_lang}")
        
    return final_lang

# ... (中间的代码保持不变) ...

class Translator:
    def __init__(self, config: Config, root: tk.Tk):
        """初始化翻译器实例"""
        self.config = config
        self.root = root
        # 修正函数名，确保模式配置能够正确加载
        try:
            self.mode_config = get_language_mode_config()
        except NameError:
            # 如果get_language_mode_config不存在，使用原有的load_mode_config
            self.mode_config = load_mode_config()
            logger.warning("使用旧的配置加载函数load_mode_config，建议更新为get_language_mode_config")
        
        # 初始化语言检测缓存 (保持不变)
        global language_detection_cache
        if language_detection_cache is None and hasattr(config, 'language_detection_cache_size'):
            language_detection_cache = LRUCache(config.language_detection_cache_size)
            logger.info(f"【初始化】语言检测缓存，容量: {config.language_detection_cache_size}")
        
        # 初始化翻译缓存 (保持不变)
        self.translation_cache = LRUCache(getattr(config, 'translation_cache_size', 50))
        logger.info(f"【初始化】翻译缓存，容量: {getattr(config, 'translation_cache_size', 50)}")

        # === 新增：初始化特定语族规则 ===
        self.specific_family_rules = {
             "cjk": resolve_cjk_ambiguity,
             # 在这里为其他需要特殊处理的语族添加规则函数
             # "indic": resolve_indic_ambiguity, 
        }
        logger.info(f"【初始化】特定语族歧义解决规则: {list(self.specific_family_rules.keys())}")
        # ==============================
        
        # 初始化服务管理器
        self.service_manager = ServiceManager(config)
        
        # 初始化翻译模式菜单项
        self.mode_menu_items = self._build_mode_menu_items()
        
        # 保持原有历史记录初始化逻辑
        self.history = {mode: [] for mode in self.mode_config["translation_modes"].keys()}
        self.translation_history = []  # 存储最近的翻译记录，用于上下文理解
        
        # 维护翻译状态
        self.translate_lock = threading.Lock()
        self.replace_lock = threading.Lock() # 检查是否使用，如果未使用可以考虑移除
        # self.suppressing_hotkeys = False # 检查是否使用
        self._in_progress = False # 初始化内部状态
        self._suppress_keyboard = False # 初始化内部状态
        self.last_replace_time = 0
        self.last_request_time = 0
        
        # 初始化事件循环，用于异步操作
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        # 存储当前GUI状态
        self.config_panel_open = False
        self.drag_data = {"x": 0, "y": 0, "dragging": False}
        
        # 初始化GUI组件
        self.frames = {}
        
        # 状态管理
        self.current_status = "就绪"
        self.api_health_status = {
            "healthy": None,
            "message": "尚未检查API状态",
            "last_check": 0
        }
        self.last_api_health_check = 0
        
        # 允许使用的颜色
        self.allowed_colors = ["white", "red", "green", "blue", "gray", "yellow", 
                            "purple", "pink", "orange", "cyan", "black", "brown"]
        
        # 创建主窗口
        logger.debug("创建翻译器主窗口")
        self.create_main_window()
        
        # 启动键盘监听线程
        self.keyboard_thread = None
        self.should_exit = threading.Event()
        self.setup_keyboard_listener()

        # 添加窗口关闭处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 使用缓存技术减少重复检查 (这里的 cache 似乎与LRUCache重复了，需要确认逻辑)
        # self.cache = {
        #     "filtered_results": {},  # 存储过滤后的结果
        #     "health_checks": {},     # 存储健康检查结果
        #     "language_detect": {}    # 存储语言检测结果
        # }
        

        
        # 异步事件循环和线程控制
        self._shutdown_event = threading.Event()
        # self.loop = asyncio.new_event_loop() # loop 在前面已经初始化
        self.loop_thread = threading.Thread(target=self.run_async_loop, daemon=True)
        self.loop_thread.start()
        
        # 启动时检查API健康状态
        asyncio.run_coroutine_threadsafe(self.check_api_health(), self.loop)

        # 创建服务管理器 (已在前面初始化)
        # self.service_manager = ServiceManager(config)

    def _build_mode_menu_items(self) -> Dict:
        """构建并同步翻译模式菜单项"""
        items = {0: {"desc": "设置"}, "00": {"desc": "清空上下文"}}
        for mode_id, mode_data in self.mode_config["translation_modes"].items():
            desc = f"{mode_data['source_lang']}-{mode_data['target_lang']}" + (f"-{mode_data['style']}" if mode_data['style'] else "")
            items[mode_id] = {"desc": desc}
        return items

    def run_async_loop(self):
        """运行异步事件循环"""
        asyncio.set_event_loop(self.loop)
        try:
            while not self._shutdown_event.is_set():
                try:
                    self.loop.run_forever()
                except Exception as e:
                    logger.error(f"异步事件循环异常: {e}，尝试重启循环")
                    time.sleep(1)
                    # 如果循环异常，确保它被重置
                    if not self.loop.is_closed():
                        self.loop.close()
                    self.loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(self.loop)
        finally:
            # 确保循环被正确关闭
            if not self.loop.is_closed():
                self.loop.close()
            logger.debug("异步事件循环已关闭")
            
    def shutdown(self):
        """关闭翻译器并释放资源"""
        logger.info("正在关闭翻译器...")
        try:
            # 停止事件循环
            if hasattr(self, 'loop') and self.loop.is_running():
                self.loop.stop()
                
            # 释放其他资源
            self.should_exit.set()
            
            # 关闭键盘监听线程
            if self.keyboard_thread and self.keyboard_thread.is_alive():
                logger.debug("等待键盘监听线程结束...")
                self.keyboard_thread.join(timeout=1.0)
                
            # 重置缓存
            if hasattr(self, 'service_manager'):
                self.service_manager.reset_network_cache()
                self.service_manager.reset_api_cache()
                
            logger.debug("翻译器资源已释放")
        except Exception as e:
            logger.error(f"关闭翻译器时出错: {e}")
            
    @property
    def in_progress(self):
        with self.translate_lock:
            return self._in_progress

    @in_progress.setter
    def in_progress(self, value):
        with self.translate_lock:
            self._in_progress = value

    @property
    def suppress_keyboard(self):
        with self.translate_lock:
            return self._suppress_keyboard

    @suppress_keyboard.setter
    def suppress_keyboard(self, value):
        with self.translate_lock:
            self._suppress_keyboard = value

    def detect_language(self, text: str, is_original: bool = True) -> str:
        """检测文本语言"""
        # 提取支持的语言代码列表
        supported_lang_codes = set()
        for mode_data in self.mode_config.get("translation_modes", {}).values():
            if "source_code" in mode_data:
                 supported_lang_codes.add(mode_data["source_code"])
            if "target_code" in mode_data:
                 supported_lang_codes.add(mode_data["target_code"])
        # 转换为 detect_language_improved 需要的格式 {code: {}}
        supported_langs_dict = {code: {} for code in supported_lang_codes}
        
        # 获取语言族配置
        language_families = getattr(self.config, 'language_families', None)
        
        # 调用新的 detect_language_improved 函数
        detected_lang_code = detect_language_improved(
            text, 
            supported_langs_dict, 
            self.mode_config.get("language_features", {}), # 安全获取
            getattr(self.config, 'lang_detection_threshold', 85.0), # 使用 getattr 获取并提供默认值
            getattr(self.config, 'short_text_threshold', 20), # 使用 getattr 获取并提供默认值
            is_original,
            language_families,
            self.specific_family_rules, # 传递初始化好的规则字典
            config=self.config # 传递整个 config 对象
        )
        
        # 保存检测到的语言代码，用于后续处理，并记录一条综合日志
        if is_original:
            self.source_lang_code = detected_lang_code
            logger.info(f"检测到原文语言: {self.source_lang_code}")
        else:
            # 当检测目标语言时，逻辑可能需要调整？
            # 目前是直接用检测结果作为反向翻译的目标语言
            self.target_lang_code = detected_lang_code
            logger.info(f"检测到目标语言文本，识别为: {detected_lang_code} (用于反向翻译)")
            
        return detected_lang_code

    def get_filter_pattern(self, lang_code: str) -> str:
        """获取过滤模式，根据不同语言可能有不同的过滤规则
        
        Args:
            lang_code: 语言代码
            
        Returns:
            过滤正则表达式模式
        """
        # 首先检查语言特定的过滤模式
        language_features = self.mode_config.get("language_features", {})
        
        # 如果语言在特征定义中存在，并且有filter_pattern定义
        if lang_code in language_features and "filter_pattern" in language_features[lang_code]:
            lang_filter = language_features[lang_code]["filter_pattern"]
            logger.debug(f"使用语言 {lang_code} 特定的过滤模式: {lang_filter}")
            return lang_filter
            
        # 查找该语言所属的语言组，可能有组级别的过滤模式
        special_language_groups = self.mode_config.get("special_language_groups", {})
        for group_name, group_info in special_language_groups.items():
            if lang_code in group_info.get("languages", []) and "filter_pattern" in group_info:
                group_filter = group_info["filter_pattern"]
                logger.debug(f"使用语言组 {group_name} 的过滤模式: {group_filter}")
                return group_filter
                
        # 如果没有特定的过滤模式，直接使用通用的非法字符定义，不输出调试日志
        return self.config.illegal_chars

    def build_prompt(self, text: str, detected_lang: str) -> tuple[str, str]:
        # 获取当前翻译模式配置
        mode_config = self.mode_config["translation_modes"].get(self.config.translation_mode, self.mode_config["translation_modes"][1])
        source_code = mode_config.get("source_code", "unknown")
        target_code = mode_config.get("target_code", "unknown")
        
        # 检查是否选择了相同语言的翻译方向
        if source_code == target_code:
            logger.warning(f"检测到翻译方向设置为相同语言: {source_code}->{target_code}，这可能导致翻译问题")
        
        # 检查输入文本的语言是否与翻译方向匹配
        if detected_lang == target_code:
            # 如果检测到的语言是目标语言，这通常意味着需要反向翻译或检查模式配置
            logger.debug(f"检测到输入语言 ({detected_lang}) 与当前模式定义的目标语言 ({target_code}) 一致，触发反向翻译流程。")

        # 构建翻译提示词
        source_lang = mode_config.get("source_lang", "未知语言")
        target_lang = mode_config.get("target_lang", "未知语言")
        style = mode_config.get("style", "")
        default_lang = mode_config.get("default_lang", source_lang)

        # 根据检测到的语言确定输入/输出语言及语码
        if detected_lang == source_code:
            input_lang = source_lang
            output_lang = target_lang
            input_code = source_code
            output_code = target_code
            style_instruction = f"，使用{style if style else '自然表达'}" if style else ""
            expected_lang_code = target_code
        elif detected_lang == target_code:
            input_lang = target_lang
            output_lang = source_lang
            input_code = target_code
            output_code = source_code
            style_instruction = ""
            expected_lang_code = source_code
        else:
            # 未知语言情况
            input_lang = "未知语言"
            output_lang = default_lang
            input_code = detected_lang  # 使用检测到的语言代码，即使它不是预设的
            output_code = source_code   # 默认输出到源语言
            style_instruction = ""
            expected_lang_code = source_code

        # 新增：检查检测到的语言是否与最终的目标翻译语言相同
        if expected_lang_code is not None and detected_lang == expected_lang_code:
             logger.debug(f" 检测到输入文本语言 ({detected_lang}) 与最终确定的目标翻译语言 ({expected_lang_code}) 相同，检查逻辑或模式设置。")

        # 安全获取语气词配置
        tone_particles = self.mode_config.get('tone_particles', {})
        # 使用通用模式作为默认值，以防language_code不存在
        default_tone_pattern = r"[\\w\\p{P}]+"
        
        # 安全获取语气词，如果不存在则使用默认模式
        source_tone = tone_particles.get(input_code, default_tone_pattern)
        target_tone = tone_particles.get(output_code, default_tone_pattern)
        
        # 记录调试信息
        if self.config.debug_mode:
            logger.debug(f"检测到语言: {detected_lang}, 输入语言: {input_lang}({input_code}), 输出语言: {output_lang}({output_code})")
            logger.debug(f"使用语气词 - 输入: {source_tone}, 输出: {target_tone}")


        # 格式化提示词模板
        prompt = UNIVERSAL_PROMPT_TEMPLATE.format(
            input_lang=input_lang,
            output_lang=output_lang,
            style_instruction=style_instruction,
            source_lang=source_lang,
            target_lang=target_lang,
            default_lang=default_lang,
            source_tone=source_tone,
            target_tone=target_tone,
        )


        context_max_count = max(self.config.context_max_count, 0)
        mode_history = self.history.get(self.config.translation_mode, [])
        if len(mode_history) > context_max_count:
            mode_history = mode_history[-context_max_count:]
        context_entries = mode_history
        if context_entries:
            logger.info(f"模式 {self.config.translation_mode} 当前上下文数量: {len(context_entries)}（最大: {context_max_count}）")
            context_list = ["原文: {}\n翻译: {}".format(entry['source'], entry['translation']) for entry in context_entries]
            context_str = "\n".join(context_list)
            if len(context_str) > 2000:
                context_str = context_str[-2000:]
                logger.debug("上下文过长，已截取最后 2000 字符")
            context_str = f"以下是最近的上下文，请参考上下文翻译以保持一致性：\n{context_str}\n\n"
        else:
            context_str = ""

        processed_text = " ".join(line.strip() for line in text.splitlines() if line.strip())
        full_prompt = f"{prompt}\n\n{context_str}将以下内容从{input_lang}翻译成{output_lang}{style_instruction}：\n{processed_text}"
        logger.debug(f"发给大模型的完整提示词:\n{full_prompt}")
        
        # 使用类变量跟踪提示词记录状态，避免重复记录
        if not hasattr(self, '_prompt_length_logged') or self._prompt_length_logged != full_prompt:
            logger.debug(f"【构建提示词】长度: {len(full_prompt)} 字符")
            self._prompt_length_logged = full_prompt
        
        if self.config.debug_mode and len(full_prompt) > 4000:
            logger.warning("提示词较长（>4000字符），可能接近模型输入限制")
        return full_prompt, expected_lang_code

    def filter_translation(self, text: str, filter_pattern: str) -> str:
        """过滤翻译结果"""
        # 使用实例变量跟踪过滤前文本记录，避免重复记录
        if not hasattr(self, '_filter_text_logged') or self._filter_text_logged != text:
            logger.debug(f"过滤前文本: {text}")
            self._filter_text_logged = text
        
        try:
            # 应用过滤模式
            filtered = regex.sub(filter_pattern, '', text)
            filtered = regex.sub(r'\s+', ' ', filtered).strip()
            
            # 记录过滤后的结果
            logger.debug(f"应用 filter_pattern {repr(filter_pattern)} 后: {filtered}")
            return filtered
        except regex.error as e:
            logger.error(f"正则表达式错误: {e}，返回原始文本")
            return text.strip()

    def contains_source_language(self, text: str, source_code: str) -> bool:
        """检查文本是否包含源语言字符，使用精确的字符范围判断

        Args:
            text: 需要检查的文本
            source_code: 源语言代码，如'zh'、'ko'、'ja'、'en'

        Returns:
            bool: 如果包含超过阈值的源语言字符则返回True
        """
        # 如果不需要检查源语言，直接返回False
        if source_code not in self.mode_config["language_features"]:
            logger.debug(f"未找到语言代码 {source_code} 的特征定义，跳过检查")
            return False
            
        # 目标语言代码 - 获取当前翻译模式的目标语言
        current_mode = self.mode_config["translation_modes"].get(
            self.config.translation_mode, 
            self.mode_config["translation_modes"][1]
        )
        target_code = current_mode["target_code"]
        
        # 如果源语言和目标语言相同，无需检查
        if source_code == target_code:
            return False
        
        # 文本长度特殊处理 - 对于短文本采用更宽松的检测策略
        is_short_text = len(text.strip()) < self.config.short_text_threshold
        
        # 直接跳过对特定语言对的检查，特别是对于配置在special_language_pairs中的语言对
        special_language_pairs = self.mode_config.get("special_language_pairs", {})
        current_pair = f"{source_code}-{target_code}"
        
        # 检查当前语言对是否在特殊处理名单中
        # 1. 精确的语言对匹配
        if current_pair in special_language_pairs and special_language_pairs[current_pair].get("skip_source_detection", False):
            logger.debug(f"语言对 {current_pair} 配置为跳过源语言检测")
            return False
        
        # 2. 通配符匹配
        for pair_pattern, config in special_language_pairs.items():
            if "-" in pair_pattern:
                src, tgt = pair_pattern.split("-")
                if (src == source_code and tgt == "*") or \
                   (src == "*" and tgt == target_code) or \
                   pair_pattern == "*-*":
                    if config.get("skip_source_detection", False):
                        logger.debug(f"语言对 {current_pair} 通过模式 {pair_pattern} 配置为跳过源语言检测")
                        return False
                    # 短文本特殊处理
                    if is_short_text and config.get("allow_short_text_mismatch", False):
                        logger.debug(f"短文本 ({len(text.strip())} 字符) 通过模式 {pair_pattern} 配置为宽松匹配")
                        return False
        
        # 3. 检查语言组匹配
        special_language_groups = self.mode_config.get("special_language_groups", {})
        
        # 找出源语言和目标语言所属的语言组
        source_groups = []
        target_groups = []
        
        for group_name, group_info in special_language_groups.items():
            languages = group_info.get("languages", [])
            if source_code in languages:
                source_groups.append(group_name)
            if target_code in languages:
                target_groups.append(group_name)
        
        # 检查语言组对配置
        for s_group in source_groups:
            for t_group in target_groups:
                group_pair = f"{s_group}-{t_group}"
                if group_pair in special_language_pairs:
                    group_config = special_language_pairs[group_pair]
                    if group_config.get("skip_source_detection", False):
                        logger.debug(f"语言对 {current_pair} 通过语言组对 {group_pair} 配置为跳过源语言检测")
                        return False
                    # 短文本特殊处理
                    if is_short_text and group_config.get("allow_short_text_mismatch", False):
                        logger.debug(f"短文本 ({len(text.strip())} 字符) 通过语言组对 {group_pair} 配置为宽松匹配")
                        return False
        
        # 如果同属于某个语言组，且该组配置为宽松检测，跳过源语言检测
        for group in source_groups:
            if group in target_groups:
                group_info = special_language_groups[group]
                if not group_info.get("strict_detection", False):
                    logger.debug(f"源语言({source_code})和目标语言({target_code})同属于语言组{group}，且配置为非严格检测")
                    return False
        
        # 如果是短文本，并且没有特殊配置，使用更宽松的规则
        if is_short_text:
            # 对于非常短的文本（如2-3个字符），直接返回False
            if len(text.strip()) <= 3:
                logger.debug(f"超短文本 ({len(text.strip())} 字符)，跳过源语言检测")
                return False
            # 对于短文本，增加容忍度
            max_allowed = min(5, max(2, int(len(text) * 0.2)))
        else:
            # 对于普通文本，使用更严格的阈值
            max_allowed = min(3, max(1, int(len(text) * 0.1)))
        
        # 进行实际检测
        if target_code in self.mode_config["language_features"]:
            target_features = self.mode_config["language_features"][target_code]
            source_features = self.mode_config["language_features"][source_code]
            
            try:
                # 获取源语言的排他性特征
                exclusive_patterns = []
                if "exclusive" in source_features:
                    exclusive_patterns = source_features["exclusive"]
                
                # 针对每个排他性字符集进行检查
                for pattern in exclusive_patterns:
                    matches = regex.findall(pattern, text)
                    if matches:
                        # 记录匹配到的实际字符，帮助调试
                        sample = matches[:3] if len(matches) > 3 else matches
                        logger.debug(f"检测到 {source_code} 特有字符: {sample}")
                        
                        # 额外检查：确保这些字符不是目标语言中正常的字符
                        if target_code in self.mode_config["language_features"]:
                            target_pattern = self.mode_config["language_features"][target_code]["pattern"]
                            if regex.search(target_pattern, ''.join(sample)):
                                logger.debug(f"虽然匹配到排他性模式，但这些字符也是目标语言({target_code})的一部分，不计为源语言")
                                continue
                                
                        return True
                
                # 宽松检测
                source_pattern = source_features["pattern"]
                matches = regex.findall(source_pattern, text)
                
                # 移除那些同时也是目标语言的字符
                if matches and target_code in self.mode_config["language_features"]:
                    target_pattern = self.mode_config["language_features"][target_code]["pattern"]
                    valid_matches = []
                    for match in matches:
                        if not regex.search(target_pattern, match):
                            valid_matches.append(match)
                    matches = valid_matches
                
                if len(matches) > max_allowed:
                    logger.debug(f"检测到 {len(matches)} 个 {source_code} 字符，超过阈值 {max_allowed}")
                    return True
            except regex.error as e:
                logger.error(f"正则表达式错误: {e}")
                return False
        
        # 如果所有检查都通过，说明文本中不包含过多源语言字符
        return False

    def check_symbol_retention(self, original: str, translated: str, target_lang_code: str, source_lang_code: str) -> bool:
        """检查翻译结果是否保留关键符号和语气
        
        Args:
            original: 原始文本
            translated: 翻译后的文本
            target_lang_code: 目标语言代码
            source_lang_code: 源语言代码
            
        Returns:
            bool: 如果翻译保留了关键符号和语气则返回True
        """
        # 获取源语言和目标语言所属的语言家族
        source_family = self._get_language_family(source_lang_code)
        target_family = self._get_language_family(target_lang_code)
        
        # 获取适用于源语言和目标语言的问号和感叹号
        source_question_marks = self._get_punctuation_for_language("question_marks", source_lang_code, source_family)
        target_question_marks = self._get_punctuation_for_language("question_marks", target_lang_code, target_family)
        
        source_exclamation_marks = self._get_punctuation_for_language("exclamation_marks", source_lang_code, source_family)
        target_exclamation_marks = self._get_punctuation_for_language("exclamation_marks", target_lang_code, target_family)
        
        # 检查原文和译文中是否包含问号
        has_source_question = any(mark in original for mark in source_question_marks)
        has_target_question = any(mark in translated for mark in target_question_marks)
        
        # 检查原文和译文中是否包含感叹号
        has_source_exclamation = any(mark in original for mark in source_exclamation_marks)
        has_target_exclamation = any(mark in translated for mark in target_exclamation_marks)
        
        # 安全获取语气词配置
        tone_particles = self.mode_config.get('tone_particles', {})
        default_tone = r'[\w\p{P}]+'  # 默认使用字母数字和标点作为通用语气词模式
        
        # 获取语言特定的语气词模式
        source_tone_pattern = tone_particles.get(source_lang_code, default_tone)
        target_tone_pattern = tone_particles.get(target_lang_code, default_tone)
        
        # 检查语气词
        try:
            original_has_tone = bool(regex.search(source_tone_pattern, original))
            translated_has_tone = bool(regex.search(target_tone_pattern, translated))
        except regex.error as e:
            logger.error(f"检查语气词时正则表达式错误: {e}")
            # 出错时保守处理，让翻译继续
            original_has_tone = False
            translated_has_tone = False

        # 记录详细调试信息
        if self.config.debug_mode:
            logger.debug(f"语言代码: 源={source_lang_code}({source_family}), 目标={target_lang_code}({target_family})")
            logger.debug(f"语气词模式: 源={source_tone_pattern}, 目标={target_tone_pattern}")
            logger.debug(f"问号检测: 源={source_question_marks}, 目标={target_question_marks}")
            logger.debug(f"感叹号检测: 源={source_exclamation_marks}, 目标={target_exclamation_marks}")
            logger.debug(f"原文语气检查: 问号={has_source_question}, 感叹号={has_source_exclamation}, 语气词={original_has_tone}")
            logger.debug(f"译文语气检查: 问号={has_target_question}, 感叹号={has_target_exclamation}, 语气词={translated_has_tone}")

        # 获取语言特定的符号保留配置
        language_pair = f"{source_lang_code}-{target_lang_code}"
        special_pairs = self.mode_config.get("special_language_pairs", {})
        
        # 默认的检查行为
        check_question = True
        check_exclamation = True
        check_tone = True
        
        # 如果有语言对特定的配置，使用该配置
        if language_pair in special_pairs:
            pair_config = special_pairs[language_pair]
            check_question = pair_config.get("check_question", True)
            check_exclamation = pair_config.get("check_exclamation", True)
            check_tone = pair_config.get("check_tone", True)
            
            if self.config.debug_mode:
                logger.debug(f"使用语言对 {language_pair} 特定的符号检查配置: "
                           f"问号={check_question}, 感叹号={check_exclamation}, 语气词={check_tone}")

        # 检查问号是否保留（如果需要检查）
        if check_question and has_source_question and not has_target_question:
            logger.warning("翻译结果丢失疑问语气")
            return False
            
        # 检查感叹号或语气是否保留（如果需要检查）
        if check_exclamation and has_source_exclamation and not (has_target_exclamation or translated_has_tone):
            logger.warning("翻译结果丢失强调语气，且无等效语气词")
            return False
            
        # 检查是否有语气词丢失（如果需要检查）
        if check_tone and original_has_tone and not translated_has_tone:
            logger.debug("原文有语气词，翻译中未保留，但继续检查字符数")

        # 对短文本跳过字符数比例检查
        total_original_chars = len(original.strip())
        if total_original_chars < self.config.short_text_threshold:
            logger.debug(f"短文本 ({total_original_chars} 字符) 跳过字符数比例检查")
            return True

        # 安全获取语言特征模式用于字符计数
        default_pattern = r'[^\s\p{P}]'  # 非空白非标点符号作为默认模式
        
        # 获取特定语言的字符匹配模式
        language_features = self.mode_config.get("language_features", {})
        source_pattern = language_features.get(source_lang_code, {}).get("pattern", default_pattern)
        target_pattern = language_features.get(target_lang_code, {}).get("pattern", default_pattern)

        try:
            original_text_chars = len(regex.findall(source_pattern, original))
            
            # 修复：当源语言和目标语言相同或属于相同语系时，使用更通用的字符检测
            if source_lang_code == target_lang_code or source_family == target_family:
                # 如果是相同语言或语系，直接计算非空白字符数
                translated_text_chars = len(regex.findall(r'[^\s]', translated))
                logger.debug(f"源语言和目标语言相同或同属一个语系，使用通用字符检测")
            else:
                # 不同语言使用目标语言特定模式
                translated_text_chars = len(regex.findall(target_pattern, translated))
                
        except regex.error as e:
            logger.error(f"字符数检查时正则表达式错误: {e}")
            # 出错时保守处理，让翻译继续
            return True

        logger.debug(f"原文字符数: {original_text_chars}，翻译字符数: {translated_text_chars}")

        # 检查翻译是否有实质内容
        if original_text_chars > 0 and translated_text_chars == 0:
            logger.warning(f"翻译结果文字内容为空: 原文 {original_text_chars} 个字符，翻译 {translated_text_chars} 个字符")
            return False
            
        # 当原文与译文使用同一语言时，应该放宽字符比例要求
        if source_lang_code == target_lang_code:
            logger.debug("源语言和目标语言相同，放宽字符比例要求")
            return True

        # 获取字符比例配置
        min_char_ratio = 0.1  # 默认最小比例
        
        # 检查是否有语言对特定的字符比例配置
        if language_pair in special_pairs and "min_char_ratio" in special_pairs[language_pair]:
            min_char_ratio = special_pairs[language_pair]["min_char_ratio"]
            logger.debug(f"使用语言对 {language_pair} 特定的最小字符比例: {min_char_ratio}")
        else:
            # 查找源语言和目标语言所属的语言组
            special_groups = self.mode_config.get("special_language_groups", {})
            source_groups = []
            target_groups = []
            
            for group_name, group_info in special_groups.items():
                languages = group_info.get("languages", [])
                if source_lang_code in languages:
                    source_groups.append(group_name)
                if target_lang_code in languages:
                    target_groups.append(group_name)
            
            # 检查语言组对的配置
            for s_group in source_groups:
                for t_group in target_groups:
                    group_pair = f"{s_group}-{t_group}"
                    if group_pair in special_pairs and "min_char_ratio" in special_pairs[group_pair]:
                        min_char_ratio = special_pairs[group_pair]["min_char_ratio"]
                        logger.debug(f"使用语言组对 {group_pair} 的最小字符比例: {min_char_ratio}")
                        break
                else:
                    continue
                break
            
        # 检查翻译结果是否过短（可能不完整）
        min_char_threshold = 10  # 默认字符阈值
        min_chars_required = original_text_chars * min_char_ratio
        
        if original_text_chars > min_char_threshold and translated_text_chars < min_chars_required:
            logger.warning(f"翻译结果文字内容显著过少: 原文 {original_text_chars} 个字符，翻译 {translated_text_chars} 个字符，"
                          f"最小要求 {min_chars_required:.1f} 个字符")
            return False

        logger.debug("翻译结果保留关键语气或足够文字内容")
        return True
        
    def _get_language_family(self, lang_code: str) -> str:
        """获取语言所属的语言家族"""
        for family, langs in self.config.language_families.items():
            if lang_code in langs:
                return family
        return "universal"  # 默认为通用
        
    def _get_punctuation_for_language(self, punct_type: str, lang_code: str, family: str = "universal") -> list:
        """获取特定语言的标点符号列表"""
        # 先检查语言特定设置
        lang_specific = self.config.language_specific_settings.get(lang_code, {})
        if punct_type in lang_specific:
            return lang_specific[punct_type]
        
        # 再检查语系设置
        family_puncts = self.config.universal_punctuation[punct_type].get(family, [])
        
        # 最后返回通用设置
        universal_puncts = self.config.universal_punctuation[punct_type]["universal"]
        
        # 合并去重
        return list(set(family_puncts + universal_puncts))

    async def check_api_health(self):
        """检查API健康状态并更新状态信息"""
        try:
            # 使用服务管理器检查网络和API状态
            is_network_ok = self.service_manager.is_network_connected()
            if not is_network_ok:
                self.api_health_status = {
                    "healthy": False,
                    "message": "网络连接不可用",
                    "last_check": time.time()
                }
                logger.warning("网络连接不可用，API健康检查中止")
                return
            
            # 直接使用全局check_api_health函数检查API健康状态
            real_api_key = get_real_api_key(self.config.api_key)
            healthy, message = await check_api_health(
                self.config.api_mode,
                real_api_key,
                self.config.model_id,
                self.config.api_base_url if self.config.api_mode == "openai" else None
            )
            
            if not healthy:
                logger.warning(f"API健康检查失败: {message}")
            
            self.api_health_status = {
                "healthy": healthy,
                "message": message,
                "last_check": time.time()
            }
            
        except Exception as e:
            logger.error(f"API健康检查过程中出错: {e}")
            self.api_health_status = {
                "healthy": False,
                "message": f"检查过程错误: {e}",
                "last_check": time.time()
            }

    async def translate_text_async(self, prompt: str, detected_lang: str, original_text: str, expected_lang_code: str) -> str:
        """异步翻译文本
        
        Args:
            prompt: 提示词
            detected_lang: 检测到的语言代码
            original_text: 原始文本
            expected_lang_code: 期望的语言代码
            
        Returns:
            翻译后的文本，或者在最终失败时返回以 \"翻译失败：\" 开头的特定错误信息。
        """
        # 检查是否被中止
        if not self.in_progress:
            logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-开始）")
            # 如果中止，也尝试恢复原文，以防万一之前的操作未完成
            # await self.replace_input_text_async(self.original_text if hasattr(self, 'original_text') else original_text)
            return "翻译已中止"

        # 翻译缓存机制 - 修改缓存键，添加方向信息，使翻译能够双向缓存
        # 标准方向缓存键：mode-source_lang-target_lang-text_hash
        cache_key = f"{self.config.translation_mode}-{detected_lang}-{expected_lang_code}-{hash(original_text)}"
        cached_translation = self.translation_cache.get(cache_key)
        if cached_translation is not None:
            logger.info(f"【缓存命中】翻译结果来自缓存。Key: {cache_key}")
            return cached_translation

        original_temperature = self.config.temperature
        if not self.api_health_status.get("healthy", True):
            logger.warning(f"API服务可能不健康: {self.api_health_status.get('message', '未知原因')}，将尝试翻译")
        
        translation_attempted_with_fallback = False
        try:
            if not self.service_manager.is_network_connected():
                # 网络问题，直接恢复原文并提示
                logger.error("翻译失败：网络连接不可用 (translate_text_async)")
                await self.replace_input_text_async(original_text) 
                return "翻译失败：网络连接不可用"

            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-网络后）")
                return "翻译已中止"
            
            # 第一次尝试使用主模型
            result = await self._translate_with_api(prompt, detected_lang, original_text, expected_lang_code, model_to_use=self.config.model_id, is_retry=False)

            # 如果第一次尝试失败 (返回了特定的失败前缀)
            if result.startswith("翻译失败：") and not ("网络连接不可用" in result or "翻译已中止" in result or "内容被API" in result):
                logger.warning(f"主模型 ({self.config.model_id}) 翻译失败: {result}。尝试使用备用模型。")
                translation_attempted_with_fallback = True
                fallback_model_id = self.config.gemini_fallback_model_id if self.config.api_mode == "gemini" else self.config.openai_fallback_model_id
                
                if not self.in_progress:
                    logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-重试前）")
                    return "翻译已中止"

                result = await self._translate_with_api(prompt, detected_lang, original_text, expected_lang_code, model_to_use=fallback_model_id, is_retry=True)

            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（translate_text_async-API后）")
                return "翻译已中止"
            
            # 最终结果处理
            if result.startswith("翻译失败："):
                logger.error(f"翻译最终失败: {result}。原始文本将恢复到输入框。")
                await self.replace_input_text_async(original_text)
                # 对于网络错误或中止，GUI的错误提示可能已在replacement_translation中处理
                # 此处返回具体错误信息供上层判断
                return result 

            if result and not result == "翻译已中止": # 成功翻译
                # 1. 保存标准方向的缓存
                self.translation_cache.put(cache_key, result)
                logger.info(f"【缓存更新】翻译结果已存入缓存。Key: {cache_key}")
                
                # 2. 同时保存反向缓存，以便下次反向翻译时可以直接使用
                # 反向缓存键：mode-target_lang-source_lang-result_hash
                reverse_cache_key = f"{self.config.translation_mode}-{expected_lang_code}-{detected_lang}-{hash(result)}"
                self.translation_cache.put(reverse_cache_key, original_text)
                logger.info(f"【缓存更新】反向翻译结果已存入缓存。Key: {reverse_cache_key}")

            if "API服务暂时不可用" in result or "503" in result: # 特殊的服务不可用错误
                logger.info("检测到服务不可用错误，执行API健康检查...")
                await self.check_api_health()
                if not self.api_health_status["healthy"]:
                    logger.warning(f"API健康检查结果: {self.api_health_status['message']}")
            
            return result if result else "翻译失败：未生成符合要求的翻译结果"
        
        except Exception as e: # 捕获translate_text_async内部的意外异常
            logger.error(f"translate_text_async 发生意外错误: {e}", exc_info=True)
            await self.replace_input_text_async(original_text)
            return f"翻译失败：发生意外错误 ({type(e).__name__})"
        finally:
            if self.config.temperature != original_temperature:
                logger.debug(f"恢复模型温度为原始值: {original_temperature}")
                self.config.temperature = original_temperature
            if hasattr(self, '_prompt_logged'):
                delattr(self, '_prompt_logged')
            if hasattr(self, '_prompt_length_logged'):
                delattr(self, '_prompt_length_logged')
            if hasattr(self, '_filter_text_logged'):
                delattr(self, '_filter_text_logged')

    async def _translate_with_api(self, prompt: str, detected_lang: str, original_text: str, expected_lang_code: str, model_to_use: str, is_retry: bool, recursive_count: int = 0) -> str:
        """使用API翻译文本
        
        Args:
            prompt (str): 提供给API的完整提示词。
            detected_lang (str): 检测到的原始文本语言代码。
            original_text (str): 用户输入的原始文本，用于特定情况下的返回。
            expected_lang_code (str): 期望翻译成的目标语言代码。
            model_to_use (str): 当前尝试使用的模型ID。
            is_retry (bool): 标记这是否是一次使用备用模型的重试。
            recursive_count (int): 用于防止因"翻译结果与原文语言相同"导致的无限递归。

        Returns:
            str: 成功时返回翻译后的文本，失败时返回以 "翻译失败：" 开头的特定错误信息。
        """
        # recursive_count 仍然用于处理"翻译结果与原文语言相同"的递归
        if recursive_count > 2: # 此限制针对"同语言"递归
            logger.error(f"翻译失败：达到最大同语言递归次数 ({recursive_count})。模型: {model_to_use}")
            return "翻译失败：出现递归翻译错误，可能是语言检测出现循环依赖"
            
        current_time = time.time()
        self.last_request_time = current_time
        
        if not hasattr(self, '_prompt_length_logged') or self._prompt_length_logged != prompt:
            logger.debug(f"【API请求】提示词长度: {len(prompt)} 字符, 模型: {model_to_use}, 是否重试: {is_retry}")
            self._prompt_length_logged = prompt
        
        current_mode = self.mode_config["translation_modes"].get(self.config.translation_mode, self.mode_config["translation_modes"][1])
        # context_max_count = max(self.config.context_max_count, 0) # 上下文已在build_prompt处理
        
        # mode_source_code = current_mode.get("source_code", "unknown") # 未直接使用
        # mode_target_code = current_mode.get("target_code", "unknown") # 未直接使用
        
        # "翻译结果与原文语言相同" 的检查与处理逻辑，只在非重试（即首次尝试）时，且 recursive_count 为0时执行
        # 因为重试时，我们是期望API能给出不同结果，即使语言相同也可能是API理解问题
        if not is_retry and detected_lang == expected_lang_code and recursive_count == 0:
            logger.info(f"检测到输入文本语言({detected_lang})与目标语言相同，无需翻译 (模型: {model_to_use})")
            return original_text
        
        # model_id 参数现在由 model_to_use 传入
        
        tcp_connector_config = self.config.tcp_connector
        connector = aiohttp.TCPConnector(
            limit=tcp_connector_config.get("limit", 10),
            ssl=False, 
            ttl_dns_cache=tcp_connector_config.get("ttl_dns_cache", 300),
            keepalive_timeout=tcp_connector_config.get("keepalive_timeout", 60),
            force_close=False 
        )
        
        timeout_config = self.config.timeout
        timeout = aiohttp.ClientTimeout(
            total=timeout_config.get("total", 30),
            connect=timeout_config.get("connect", 10),
            sock_connect=timeout_config.get("sock_connect", 10),
            sock_read=timeout_config.get("sock_read", 20)
        )

        api_key = get_real_api_key(self.config.api_key)
        if not api_key:
            # 这是配置问题，重试也无用
            return "翻译失败：API密钥未设置或解密失败"

        headers = {"Content-Type": "application/json"}
        request_data = {}
        url = ""
        extract_fn: Callable[[Dict], str] = lambda res: ""
        check_fn: Callable[[Dict], bool] = lambda res: False

        try:
            if self.config.api_mode == "gemini":
                import urllib.parse
                encoded_api_key = urllib.parse.quote(api_key.strip())
                url = f"https://generativelanguage.googleapis.com/v1beta/models/{model_to_use}:generateContent?key={encoded_api_key}"
                safety_settings = self.config.safety_settings
                request_data = {
                    "contents": [{"parts": [{"text": prompt}]}],
                    "generationConfig": {
                        "temperature": self.config.temperature,
                        "maxOutputTokens": self.config.max_output_tokens,
                        "topP": self.config.top_p,
                        "topK": self.config.top_k
                    },
                    "safetySettings": safety_settings.get("gemini", [])
                }
                check_fn = lambda r: r and "candidates" in r and r["candidates"]
                def extract_gemini(result: Dict) -> str:
                    if result and "candidates" in result and result["candidates"]:
                        candidate = result["candidates"][0]
                        finish_reason = candidate.get("finishReason", "")
                        if finish_reason == "SAFETY":
                            logger.warning(f"Gemini API 内容因安全原因被过滤 (模型: {model_to_use})")
                            return "翻译失败：内容被API安全系统过滤 (Gemini)"
                        if finish_reason == "RECITATION": # Google新增的finishReason
                            logger.warning(f"Gemini API 内容因背诵受限 (模型: {model_to_use})")
                            return "翻译失败：内容因API背诵限制被过滤 (Gemini)"                            
                        if finish_reason == "OTHER":
                            logger.warning(f"Gemini API 因其他原因完成，可能存在问题 (模型: {model_to_use})")
                            # 可以考虑返回特定错误或空字符串，让上层重试
                            return "翻译失败：API因未知原因提前终止 (Gemini)" 
                        if "content" in candidate and "parts" in candidate["content"]:
                            parts = candidate["content"]["parts"]
                            if parts and "text" in parts[0]:
                                return parts[0]["text"].strip()
                    # 如果 Gemini API 返回空结果或错误结构，但没有明确的 error 对象 (例如 HTTP 200 但内容不对)
                    if not (result and "candidates" in result and result["candidates"] and \
                            "content" in result["candidates"][0] and "parts" in result["candidates"][0]["content"] and \
                            result["candidates"][0]["content"]["parts"] and "text" in result["candidates"][0]["content"]["parts"][0]):
                        logger.warning(f"Gemini API 返回了非预期的空/错误结构 (模型: {model_to_use}): {result}")
                        return "翻译失败：API返回空或无效响应 (Gemini)" # 返回特定错误信息
                    return "" # 默认返回空字符串，表示未提取到内容
                extract_fn = extract_gemini

            elif self.config.api_mode == "openai":
                api_base_url = self.config.api_base_url
                api_endpoint = self.config.api_endpoint
                if not api_endpoint.startswith('/'):
                    api_endpoint = '/' + api_endpoint
                url = f"{api_base_url.rstrip('/')}{api_endpoint}"
                headers["Authorization"] = f"Bearer {api_key.strip()}"
                messages = [{"role": "user", "content": prompt}]
                request_data = {
                    "model": model_to_use, # 使用传入的 model_to_use
                    "messages": messages,
                    "temperature": self.config.temperature,
                    "max_tokens": self.config.max_output_tokens,
                    "top_p": self.config.top_p,
                    "frequency_penalty": self.config.frequency_penalty,
                    "presence_penalty": self.config.presence_penalty
                }
                check_fn = lambda r: r and "choices" in r and r["choices"]
                def extract_openai(result: Dict) -> str:
                    if result and "choices" in result and result["choices"]:
                        choice = result["choices"][0]
                        finish_reason = choice.get("finish_reason", "")
                        if finish_reason == "content_filter":
                            logger.warning(f"OpenAI API 内容被过滤器阻止 (模型: {model_to_use})")
                            return "翻译失败：内容被API内容过滤器阻止 (OpenAI)"
                        if finish_reason == "length":
                            logger.warning(f"OpenAI API因达到max_tokens而中止 (模型: {model_to_use})")
                            # 这种情况通常翻译了一部分，可以考虑返回部分结果或特定错误
                        if "message" in choice and "content" in choice["message"]:
                            return choice["message"]["content"].strip()
                    # 如果 OpenAI API 返回空结果或错误结构
                    if not (result and "choices" in result and result["choices"] and \
                            "message" in result["choices"][0] and "content" in result["choices"][0]["message"]):
                        logger.warning(f"OpenAI API 返回了非预期的空/错误结构 (模型: {model_to_use}): {result}")
                        return "翻译失败：API返回空或无效响应 (OpenAI)" # 返回特定错误信息
                    return "" # 默认返回空字符串
                extract_fn = extract_openai
            else:
                # 不支持的API模式，重试也无用
                return f"翻译失败：不支持的API模式 {self.config.api_mode}"

            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                if self.config.show_gui_progress and gui_handler is not None:
                    current_progress = 70 if is_retry else 50 # 重试时进度条可以靠后一些
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", current_progress, f"调用API ({self.config.api_mode} - {model_to_use})...")
                
                # 检查是否被中止
                if not self.in_progress:
                    logger.info(f"检测到翻译被中止，主动退出翻译流程 (_translate_with_api-请求前, 模型: {model_to_use})")
                    return "翻译已中止"

                async with session.post(url, headers=headers, json=request_data) as response:
                    response_text = await response.text()
                    # 检查是否被中止
                    if not self.in_progress:
                        logger.info(f"检测到翻译被中止，主动退出翻译流程 (_translate_with_api-收到响应后, 模型: {model_to_use})")
                        return "翻译已中止"
                    
                    if response.status == 429: # Too Many Requests
                        logger.error(f"API请求过于频繁 (429)，模型: {model_to_use}。请稍后重试。信息: {response_text}")
                        # 对于429，如果是首次尝试，上层会触发重试（可能使用不同模型）。如果已是重试，则失败。
                        return f"翻译失败：API请求过于频繁 (429)，请稍后再试 (模型: {model_to_use})"
                    if response.status == 503: # Service Unavailable
                        logger.error(f"API服务暂时不可用 (503)，模型: {model_to_use}。信息: {response_text}")
                        return f"翻译失败：API服务暂时不可用 (503) (模型: {model_to_use})"
                    if response.status >= 400: # 其他客户端或服务器错误
                        logger.error(f"API请求失败，状态码: {response.status}，模型: {model_to_use}。响应: {response_text}")
                        err_msg_prefix = f"翻译失败：API错误 {response.status}"
                        try:
                            error_json = json.loads(response_text)
                            api_err_msg = error_json.get("error", {}).get("message", "")
                            if api_err_msg:
                                err_msg_prefix += f" - {api_err_msg[:100]}" # 截断过长的API错误信息
                        except json.JSONDecodeError:
                            pass # 响应不是JSON，使用通用错误信息
                        return f"{err_msg_prefix} (模型: {model_to_use})"

                    try:
                        result_json = json.loads(response_text)
                    except json.JSONDecodeError:
                        logger.error(f"无法解析API响应为JSON，模型: {model_to_use}。响应: {response_text}")
                        return f"翻译失败：API响应格式错误 (非JSON) (模型: {model_to_use})"

                    if not check_fn(result_json): # 检查响应结构是否符合预期
                        logger.error(f"API响应结构不符合预期，模型: {model_to_use}。响应: {result_json}")
                        # Gemini的错误通常在 result_json["error"] 中
                        if self.config.api_mode == "gemini" and "error" in result_json:
                            gemini_error = result_json["error"].get("message", "未知Gemini错误")
                            return f"翻译失败：API返回错误 - {gemini_error[:100]} (Gemini, 模型: {model_to_use})"
                        return f"翻译失败：API响应结构无效 (模型: {model_to_use})"
                    
                    translated_text = extract_fn(result_json)

                    if not translated_text.strip(): # API返回了空翻译
                        # 这可能是由内容过滤、无效响应或模型未能生成文本引起的
                        # extract_fn 内部应该已经处理了安全过滤等，并返回了特定错误信息
                        # 如果 extract_fn 返回空，但不是特定错误，则认为是一般性空响应
                        if translated_text.startswith("翻译失败："): # extract_fn 返回了特定失败信息
                            logger.warning(f"{translated_text} (模型: {model_to_use})")
                            return translated_text
                        else:
                            logger.warning(f"API返回空翻译结果 (模型: {model_to_use})。响应: {result_json}")
                            return f"翻译失败：API返回空翻译结果 (模型: {model_to_use})"
                    
                    # 成功提取到翻译文本
                    # 检查翻译结果是否与原文语言和内容大幅相似 (只在首次尝试时，避免重试时再次进入此逻辑)
                    if not is_retry and self.config.api_mode == "gemini" and detected_lang == expected_lang_code: 
                        # 对于Gemini, 如果输入输出语言相同，有时它会返回几乎原文的内容
                        # 增加一个相似度检查，如果过于相似，也认为是一种需要调整的翻译问题
                        similarity_threshold_same_lang = 0.9 # 可以调整这个阈值
                        similarity = self.calculate_text_similarity(original_text, translated_text)
                        if similarity >= similarity_threshold_same_lang:
                            logger.warning(f"Gemini返回结果与原文 ({detected_lang}) 高度相似 ({similarity:.2f})，可能未翻译。将尝试调整。模型: {model_to_use}")
                            # 对于这种情况，我们不立即失败，而是允许上层的 recursive_count 机制处理（如果适用）
                            # 或者，如果 recursive_count 逻辑不再适用，则这里应该返回一个错误，触发重试
                            # 当前保留，依赖 recursive_count (如果build_prompt会修改prompt)
                            pass # 允许 recursive_count 机制（如果 applicable for same lang）

                    # 翻译成功，但仍需检查是否与原文的语言代码相同 (非内容相似，而是语言代码)
                    # 这部分递归调用现在由 recursive_count 控制
                    translated_lang_detected = self.detect_language(translated_text, is_original=False)
                    if translated_lang_detected == detected_lang and detected_lang != expected_lang_code and recursive_count < 2:
                        logger.warning(f"翻译结果 ({translated_lang_detected}) 与源语言 ({detected_lang}) 相同，但期望为 ({expected_lang_code})。模型: {model_to_use}。尝试调整提示词并重试 (第 {recursive_count + 1} 次)")
                        # 修改提示，要求更严格的语言输出
                        new_prompt = prompt + f"\n请严格确保输出语言为 {self.get_language_name(expected_lang_code)} ({expected_lang_code})，而不是 {self.get_language_name(detected_lang)}。"
                        return await self._translate_with_api(new_prompt, detected_lang, original_text, expected_lang_code, model_to_use, is_retry, recursive_count + 1)
                    
                    logger.info(f"API翻译成功。模型: {model_to_use}")
                    return translated_text

        except aiohttp.ClientConnectorError as e:
            logger.error(f"API连接错误 (模型: {model_to_use}): {e}")
            # 网络连接问题，通常不需要切换模型重试，因为可能是DNS或本地网络问题
            return f"翻译失败：网络连接错误 (模型: {model_to_use}) - {type(e).__name__}"
        except asyncio.TimeoutError: # aiohttp.ClientTimeout 通常会转换为这个
            logger.error(f"API请求超时 (模型: {model_to_use})")
            return f"翻译失败：API请求超时 (模型: {model_to_use})"
        except aiohttp.ClientError as e: # 其他 aiohttp 客户端错误
            logger.error(f"API客户端错误 (模型: {model_to_use}): {e}")
            return f"翻译失败：API客户端错误 (模型: {model_to_use}) - {type(e).__name__}"
        except Exception as e: # 其他所有意外错误
            logger.error(f"翻译过程中发生未知错误 (模型: {model_to_use}): {e}", exc_info=True)
            return f"翻译失败：发生未知API错误 (模型: {model_to_use}) - {type(e).__name__}"

    def assess_translation_quality(self, original_text: str, translated_text: str, 
                                   source_lang_code: str, target_lang_code: str,
                                   detected_source_lang: str) -> Tuple[str, float, List[str]]:
        """评估翻译质量 (简化版)
        
        Args:
            original_text: 原始文本
            translated_text: 翻译后的文本
            source_lang_code: 模式定义的源语言代码
            target_lang_code: 模式定义的目标语言代码
            detected_source_lang: 实际检测到的原文语言

        Returns:
            Tuple[str, float, List[str]]: (评估标签, 评估分数, 问题列表)
            评估标签: "良好", "一般", "较差"
            评估分数: 0.0 - 1.0 (越高越好)
            问题列表: 检测到的具体问题描述
        """
        issues = []
        score = 1.0 # 初始满分

        # 1. 检查译文是否为空或通用错误提示
        if not translated_text.strip():
            issues.append("翻译结果为空")
            return "较差", 0.0, issues
        
        common_error_indicators = ["[翻译失败]", "error:", "<unk>", "i apologize, but i cannot"]
        for indicator in common_error_indicators:
            if indicator.lower() in translated_text.lower():
                issues.append(f"翻译结果包含错误指示符: '{indicator}'")
                score *= 0.1 # 严重问题
                # return "较差", 0.1, issues # 可以直接返回

        # 2. 复用现有检查：源语言残留
        # 注意：contains_source_language 检查的是译文中是否包含 *检测到的原文语言* 的字符
        # 这里我们应该传入API调用时确定的 detected_lang (即这里的 detected_source_lang)
        if self.contains_source_language(translated_text, detected_source_lang):
            issues.append(f"翻译结果可能残留源语言 ({detected_source_lang}) 内容")
            score *= 0.7 

        # 3. 复用现有检查：符号和内容保留
        # check_symbol_retention 使用的是模式定义的 source_lang_code 和 target_lang_code
        if not self.check_symbol_retention(original_text, translated_text, target_lang_code, source_lang_code):
            # check_symbol_retention 内部会打日志，这里只记录问题
            issues.append("翻译结果可能丢失关键语气、符号或内容不足")
            score *= 0.65

        # 4. 文本相似度检查
        similarity = self.calculate_text_similarity(original_text, translated_text)
        # 阈值可配置或调整
        # same_language_match_threshold 来自配置，通常用于检测是否 *未翻译*
        # 如果翻译后的语言和原文不同，但相似度仍然很高，也可能是问题 (过于字面)
        # 如果翻译后的语言和原文相同 (例如，API错误返回原文)，则 similarity 会很高
        
        # 首先检测翻译结果的语言
        # 这里需要一个轻量级的语言检测，或者假设翻译API正确返回了目标语言
        # 为避免循环依赖和复杂性，暂时不在这里再次调用完整语言检测
        # 而是依赖之前的相似度重试逻辑中对语言的判断
        
        # 规则：如果API应该翻译但结果与原文高度相似
        # 这里的逻辑需要小心，因为_translate_with_api中已有相似度过高后的重试逻辑
        # 此处的评估是在重试逻辑 *之后* (如果发生重试的话)
        # 所以，如果执行到这里，相似度仍然很高，那可能是一个更顽固的问题
        high_similarity_threshold = self.config.same_language_match_threshold 
        # (或者一个略低于它的值，比如 high_similarity_threshold - 0.05)
        
        # 检查是否真的翻译成了目标语言（如果可以低成本判断）
        # 假设翻译API大致正确，若相似度高，且目标语言与源语言不同，则可能是过于字面
        if detected_source_lang != target_lang_code and similarity > high_similarity_threshold - 0.1: # 阈值略微放宽
             issues.append(f"翻译结果与原文相似度过高 ({similarity:.2f})，可能翻译质量不高或过于字面。")
             score *= 0.75
        elif detected_source_lang == target_lang_code and similarity < 0.3: # 同语言互译（例如风格转换）但不应改变太多内容
             issues.append(f"同语言翻译结果与原文相似度过低 ({similarity:.2f})，可能内容丢失。")
             score *= 0.6


        # 5. 重复词/短语检测 (简化版)
        words = regex.findall(r'\\b\\w+\\b', translated_text.lower())
        if len(words) > 10: # 只对稍长的文本检查
            word_counts = collections.Counter(words)
            most_common = word_counts.most_common(1)
            if most_common and most_common[0][1] > len(words) * 0.3 and most_common[0][1] > 3: # 一个词占比超过30%且出现超过3次
                issues.append(f"翻译结果中词语 '{most_common[0][0]}' 重复次数过多")
                score *= 0.8

        # 根据分数和问题数量判断最终标签
        final_label = "良好"
        if score < 0.6 or len(issues) >= 3:
            final_label = "较差"
        elif score < 0.85 or len(issues) >= 1:
            final_label = "一般"
            
        logger.info(f"翻译质量评估: {final_label} (得分: {score:.2f}), 问题: {issues if issues else '无明显问题'}")
        return final_label, score, issues

    def get_clipboard_text(self) -> Optional[str]:
        """获取剪贴板文本"""
        for attempt in range(5):
            try:
                text = pyperclip.paste()
                if text:
                    return text
            except Exception as e:
                logger.warning(f"剪贴板读取失败，第{attempt+1}次尝试: {e}")
            time.sleep(0.2)
        logger.error("无法获取剪贴板内容")
        return None

    def replace_input_text(self, text: str):
        """替换输入框文本"""
        with self.replace_lock:
            current_time = time.time()
            if current_time - self.last_replace_time < 0.5:
                logger.debug("替换操作过于频繁，跳过")
                return
            self.suppress_keyboard = True
            try:
                pyperclip.copy(text)
                # 减少等待时间
                time.sleep(0.05)
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.02)
                pyautogui.press('delete')
                time.sleep(0.02)
                pyautogui.hotkey('ctrl', 'v')
                time.sleep(0.05)
                self.last_replace_time = current_time
                logger.debug(f"输入框内容已替换为: {text}")
            except Exception as e:
                logger.error(f"替换输入框内容失败: {e}")
            finally:
                self.suppress_keyboard = False

    async def replace_input_text_async(self, text: str):
        """异步替换输入框文本"""
        # 使用run_in_executor代替to_thread以减少开销
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, self.replace_input_text, text)

    async def replacement_translation(self):
        """替换输入文本翻译功能，处理剪贴板内容"""
        try:
            self.in_progress = True
            # 检查是否被中止
            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-开始）")
                return
            text = self.get_clipboard_text()
            if not text or len(text.strip()) == 0:
                logger.warning("剪贴板内容为空")
                self.in_progress = False
                return
            self.original_text = text
            if len(text) > self.config.max_text_length:
                logger.error("文本超出长度限制")
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "network_error", 0, "文本过长")
                    self.root.after(0, gui_handler.hide_progress_indicator)
                self.in_progress = False
                return
            logger.info(f"【原文】\n{text}")
            detected_lang = self.detect_language(text, is_original=True)
            current_mode = self.mode_config["translation_modes"].get(
                self.config.translation_mode, 
                self.mode_config["translation_modes"][1]
            )
            if detected_lang == "unknown":
                logger.warning("无法准确识别语言，尝试使用language_features进行识别")
                supported_langs = set()
                for mode in self.mode_config["translation_modes"].values():
                    if "source_code" in mode:
                        supported_langs.add(mode["source_code"])
                    if "target_code" in mode:
                        supported_langs.add(mode["target_code"])
                for lang_code, feature in self.mode_config["language_features"].items():
                    if lang_code in supported_langs and "pattern" in feature:
                        try:
                            pattern = regex.compile(feature["pattern"])
                            if pattern.search(text):
                                detected_lang = lang_code
                                logger.info(f"通过语言特征检测到语言: {detected_lang} ({feature.get('desc', '未知语言特征')})")
                                break
                        except regex.error as e:
                            logger.error(f"语言 {lang_code} 的正则表达式错误: {e}")
                if detected_lang == "unknown":
                    detected_lang = current_mode["source_code"]
                    logger.info(f"无法识别语言，使用当前模式默认源语言: {detected_lang}")
            expected_lang_code = None
            if detected_lang == current_mode["target_code"]:
                expected_lang_code = current_mode["source_code"]
                logger.info(f"检测到目标语言文本，执行反向翻译为: {expected_lang_code}")
            else:
                expected_lang_code = current_mode["target_code"]
                logger.info(f"执行正向翻译为: {expected_lang_code}")
            logger.info(f"当前模型温度: {self.config.temperature}, Top-P: {self.config.top_p}")
            prompt, prompt_lang = self.build_prompt(text, detected_lang)
            if self.config.show_gui_progress and gui_handler is not None:
                self.root.after(0, gui_handler.show_progress_indicator)
                gui_handler.update_progress_indicator("preparing", 10)
                await asyncio.sleep(0.1)
            is_network_ok = self.service_manager.is_network_connected()
            if not is_network_ok:
                logger.error("网络连接不可用")
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "network_error", 0)
                    self.root.after(0, gui_handler.hide_progress_indicator)
                await self.replace_input_text_async(self.original_text)
                self.in_progress = False
                return
            # 检查是否被中止
            if not self.in_progress:
                logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-翻译前）")
                return
            try:
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", 40)
                # 检查是否被中止
                if not self.in_progress:
                    logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-调用翻译前）")
                    return
                translated = await self.translate_text_async(prompt, detected_lang, text, expected_lang_code)
                # 检查是否被中止
                if not self.in_progress:
                    logger.info("检测到翻译被中止，主动退出翻译流程（replacement_translation-翻译后）")
                    return
                if translated.startswith("翻译失败："):
                    if "网络连接不可用" in translated:
                        logger.error("翻译过程中网络连接不可用")
                        if self.config.show_gui_progress and gui_handler is not None:
                            self.root.after(0, gui_handler.update_progress_indicator, "network_error", 40)
                            self.root.after(0, gui_handler.hide_progress_indicator)
                        await self.replace_input_text_async(self.original_text)
                        self.in_progress = False
                        return
                    if "网络" in translated:
                        is_network_ok = self.service_manager.is_network_connected(force_check=True)
                        if not is_network_ok:
                            logger.error("网络连接不可用，无法继续翻译")
                            if self.config.show_gui_progress and gui_handler is not None:
                                self.root.after(0, gui_handler.update_progress_indicator, "network_error", 40)
                                self.root.after(2000, gui_handler.hide_progress_indicator)
                            await self.replace_input_text_async(self.original_text)
                            self.in_progress = False
                            return
                    logger.error(f"翻译失败: {translated}")
                    if self.config.show_gui_progress and gui_handler is not None:
                        self.root.after(0, gui_handler.update_progress_indicator, "api_error", 60)
                        self.root.after(0, gui_handler.hide_progress_indicator)
                    await self.replace_input_text_async(self.original_text)
                    self.in_progress = False
                    return
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", 80, "准备替换文本...")
                logger.info(f"【翻译结果】\n{translated}")
                if self.config.translation_mode not in self.history:
                    self.history[self.config.translation_mode] = []
                current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.history[self.config.translation_mode].append({
                    "time": current_time,
                    "source": text,
                    "source_lang": detected_lang,
                    "translation": translated,
                    "target_lang": expected_lang_code
                })
                # 新增：记录上一次成功翻译的目标语言代码
                self.last_translation_target_code = expected_lang_code
                logger.debug(f"记录上次翻译目标语言: {self.last_translation_target_code}")

                context_max_count = max(self.config.context_max_count, 0)
                if context_max_count > 0:
                    while len(self.history[self.config.translation_mode]) > context_max_count:
                        self.history[self.config.translation_mode].pop(0)
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "translating", 90, "替换文本中...")
                await self.replace_input_text_async(translated)
                logger.info("翻译完成，结果已替换输入框内容")
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, lambda: gui_handler.update_progress_indicator("success", 100, "完成"))
                    self.root.after(0, gui_handler.hide_progress_indicator)
                self.in_progress = False
            except Exception as e:
                logger.error(f"翻译过程中出现未知错误: {str(e)}")
                await self.replace_input_text_async(self.original_text)
                if self.config.show_gui_progress and gui_handler is not None:
                    self.root.after(0, gui_handler.update_progress_indicator, "api_error", 40, f"翻译错误: {str(e)}")
                    self.root.after(0, gui_handler.hide_progress_indicator)
                self.in_progress = False
        except Exception as e:
            logger.error(f"替换输入文本失败: {e}")
            if hasattr(self, 'original_text'):
                await self.replace_input_text_async(self.original_text)
            if self.config.show_gui_progress and gui_handler is not None:
                self.root.after(0, gui_handler.hide_progress_indicator)
            self.in_progress = False

    def clear_all_context(self):
        """清空所有模式的上下文"""
        for mode in self.history:
            self.history[mode].clear()
        logger.info("所有翻译模式的上下文已清空")
        
        # 清空翻译缓存
        if hasattr(self, 'translation_cache'):
            self.translation_cache.clear()
            logger.info(f"翻译缓存已清空，共清除了{len(self.translation_cache)}个缓存项")
        
        # 清空上下文后，进行一次垃圾回收
        logger.debug("执行垃圾回收 (清空上下文和缓存后)")
        gc.collect()

    # 添加create_main_window方法
    def create_main_window(self):
        """创建主窗口相关设置"""
        logger.debug("初始化翻译器主窗口设置")
        
        # 初始化实例变量
        self._in_progress = False  # 译文替换进行中标志
        self._suppress_keyboard = False  # 键盘事件抑制标志
        
        # 设置窗口属性
        if self.root:
            self.root.title("翻译助手")
            self.root.withdraw()  # 隐藏主窗口，只在任务栏显示图标
            
            # 防止root窗口被误关闭，改为调用translator的关闭方法
            self.root.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # 初始化GUI状态变量
        self.gui_variables = {
            "status": tk.StringVar(value="就绪"),
            "current_mode": tk.StringVar(value=str(self.config.translation_mode))
        }
        
        logger.debug("主窗口设置完成")

    def on_close(self):
        """处理窗口关闭事件"""
        self.shutdown()
        if self.root:
            self.root.destroy()

    def setup_keyboard_listener(self):
        """设置键盘监听器"""
        logger.debug("设置键盘监听器")
        # 键盘监听器在主函数中创建，此处不需要再创建

    def calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两段文本的字符相似度，综合考虑多种因素
        
        Args:
            text1: 第一段文本
            text2: 第二段文本
            
        Returns:
            float: 相似度分数，范围从0到1，越大表示越相似
        """
        # 移除标点符号和空白字符
        def clean_text(text: str) -> str:
            punctuation_pattern = self.config.common_symbols
            try:
                cleaned = regex.sub(punctuation_pattern, '', text)
                cleaned = ''.join(cleaned.split())
                return cleaned
            except regex.error as e:
                logger.error(f"清理文本时正则表达式错误: {e}")
                return text.strip()

        text1_clean = clean_text(text1)
        text2_clean = clean_text(text2)

        if not text1_clean or not text2_clean:
            return 0.0

        if text1_clean == text2_clean:
            return 1.0

        len1, len2 = len(text1_clean), len(text2_clean)

        # 短文本优化：使用Jaccard相似度或编辑距离
        # 短文本阈值可以从配置中读取，如果配置中没有，则使用默认值15
        short_text_eval_threshold = getattr(self.config, 'short_text_eval_threshold', 15)
        if len1 < short_text_eval_threshold and len2 < short_text_eval_threshold:
            # 使用字符级别的Jaccard相似度
            set1 = set(text1_clean)
            set2 = set(text2_clean)
            intersection_len = len(set1.intersection(set2))
            union_len = len(set1.union(set2))
            if union_len == 0:
                return 1.0 if intersection_len == 0 else 0.0 # 两个空字符串是相似的
            jaccard_char = intersection_len / union_len
            
            # 对于非常短的文本，可以考虑Levenshtein距离（编辑距离）
            # Levenshtein距离计算相对耗时，这里仅使用Jaccard作为示例，可根据需要替换或增加
            # levenshtein_distance = SequenceMatcher(None, text1_clean, text2_clean).ratio()
            # logger.debug(f"短文本相似度 (Jaccard Char): {jaccard_char:.3f}")
            return jaccard_char

        # N-gram Jaccard相似度 (以2-gram为例)
        def get_ngrams(text: str, n: int) -> set:
            if len(text) < n:
                return {text} # 如果文本长度小于n，则整个文本作为一个gram
            return set(text[i:i+n] for i in range(len(text) - n + 1))

        ngrams1 = get_ngrams(text1_clean, 2)
        ngrams2 = get_ngrams(text2_clean, 2)
        
        if not ngrams1 and not ngrams2: # 两者都无法生成ngrams（例如都是单字符）
            return 1.0 if text1_clean == text2_clean else 0.0
        if not ngrams1 or not ngrams2: # 其中一个无法生成ngrams
             # 如果一个是单字符，一个是多字符，相似度很低
             return 0.1 if (len1 == 1 and len2 >1) or (len2 == 1 and len1 > 1) else 0.0

        intersection_ngrams = len(ngrams1.intersection(ngrams2))
        union_ngrams = len(ngrams1.union(ngrams2))
        jaccard_ngram = 0.0
        if union_ngrams > 0:
            jaccard_ngram = intersection_ngrams / union_ngrams
        # logger.debug(f"N-gram Jaccard (2-gram): {jaccard_ngram:.3f}")

        # SequenceMatcher (Levenshtein-like) 相似度
        # SequenceMatcher对于长文本也比较有效，且能捕捉顺序信息
        seq_matcher_ratio = SequenceMatcher(None, text1_clean, text2_clean).ratio()
        # logger.debug(f"SequenceMatcher Ratio: {seq_matcher_ratio:.3f}")

        # 长度惩罚因子 (可选，但对于差异较大的文本有意义)
        len_diff_ratio = 1.0
        if max(len1, len2) > 0:
            len_diff_ratio = 1.0 - (abs(len1 - len2) / max(len1, len2))
        # logger.debug(f"Length Difference Ratio: {len_diff_ratio:.3f}")

        # 综合加权
        # 权重可根据实际效果调整
        # 对于大多数情况，SequenceMatcher 的权重可以高一些，因为它考虑了顺序
        # N-gram Jaccard 补充了局部短语的相似性
        # 长度惩罚作为调整项
        final_similarity = (seq_matcher_ratio * 0.6) + (jaccard_ngram * 0.3) + (len_diff_ratio * 0.1)
        
        # 如果文本很短，但SequenceMatcher给出了高分，可以适当提高权重
        if (len1 < short_text_eval_threshold or len2 < short_text_eval_threshold) and seq_matcher_ratio > 0.8:
            final_similarity = max(final_similarity, seq_matcher_ratio * 0.8) # 给予更高的权重

        # logger.debug(f"原始文本1: '{text1[:30]}...', 原始文本2: '{text2[:30]}...'")
        # logger.debug(f"清理后文本1: '{text1_clean[:30]}...', 清理后文本2: '{text2_clean[:30]}...'")
        # logger.info(f"计算文本相似度: {final_similarity:.3f} (SeqMatch: {seq_matcher_ratio:.3f}, Ngram Jac: {jaccard_ngram:.3f}, LenDiff: {len_diff_ratio:.3f})")

        return min(max(0.0, final_similarity), 1.0) # 确保结果在0-1之间

    def get_language_name(self, lang_code: str) -> str:
        """获取语言代码对应的语言名称
        
        Args:
            lang_code: ISO 639-1语言代码
            
        Returns:
            str: 语言名称，如果找不到对应的语言名称则返回原始代码
        """
        try:
            # 从配置中获取语言映射
            language_map = self.config.get('language_map', {})
            # 返回映射的语言名称，如果不存在则返回原始代码
            return language_map.get(lang_code, lang_code)
        except Exception as e:
            logger.error(f"获取语言名称时出错: {e}")
            return lang_code

    def translate_text(self, text: str) -> str:
        """翻译文本
        
        Args:
            text: 要翻译的文本
            
        Returns:
            str: 翻译后的文本
        """
        try:
            # 保存原文，用于失败时恢复
            self.original_text = text
            
            # ... existing translation code ...
            
        except Exception as e:
            logger.error(f"翻译过程中出现未知错误: {e}")
            # 确保恢复原文
            self.restore_original_text()
            raise
            
    def restore_original_text(self) -> None:
        """恢复原始文本到输入框"""
        try:
            if hasattr(self, 'original_text'):
                # 使用replace_input_text方法而非直接操作GUI
                self.replace_input_text(self.original_text)
                logger.info("已恢复原文到输入框")
        except Exception as e:
            logger.error(f"恢复原文时出错: {e}")

    # 翻译取消功能已移除

class GUIHandler:
    def __init__(self, root: Optional[tk.Tk]):
        self.root = root
        # 不再在初始化时隐藏主窗口，避免影响mainloop
        self.progress_indicator: Optional[tk.Toplevel] = None
        self.progress_label: Optional[tk.Label] = None
        self.progress_bar: Optional[ttk.Progressbar] = None
        self.status_label: Optional[tk.Label] = None
        self.progress_after_id: Optional[str] = None
        self.is_showing: bool = False
        self.dots_count = 0
        self.dots_animation_id = None
        self.current_progress = 0
        self.target_progress = 0
        self.animation_speed = 10  # 进度条动画速度
        self.animation_id = None
        
        # 设置主题颜色
        self.theme = {
            "background": "#f0f0f5",        # 背景色
            "text": "#333333",              # 主文本色
            "secondary_text": "#666666",    # 次要文本色
            "accent": "#4a86e8",            # 强调色（进度条）
            "success": "#2ecc71",           # 成功色
            "error": "#e74c3c",             # 错误色
            "border": "#dddddd"             # 边框色
        }
        
        # 配置进度条样式
        self._setup_styles()
        
        # 简化为只使用中文文本提示
        self.progress_text = "翻译中"
        
        # 简化为中文状态提示
        self.status_texts = {
            "network_error": "网络连接不可用",
            "api_error": "API服务不可用",
            "success": "翻译完成",
            "preparing": "准备翻译",
            "translating": "正在翻译"
        }

    def _setup_styles(self):
        """设置自定义样式"""
        if self.root:
            style = ttk.Style()
            # 创建自定义进度条样式
            style.configure(
                "Slim.Horizontal.TProgressbar",
                troughcolor=self.theme["border"],
                background=self.theme["accent"],
                thickness=4,
                borderwidth=0,
                relief="flat"
            )
            
    def _apply_window_styling(self, window):
        """应用窗口样式"""
        if window:
            try:
                # 设置窗口背景色
                window.configure(background=self.theme["background"])
                
                # 设置完全不透明
                window.attributes("-alpha", 1.0)
                
                # 设置置顶
                window.attributes("-topmost", True)
                
                # 如果是Windows平台，设置窗口为工具窗口并确保不获取焦点
                if sys.platform == "win32":
                    try:
                        # 设置为工具窗口
                        window.attributes("-toolwindow", 1)
                        
                        # 使用Windows API进一步确保窗口不获取焦点
                        hwnd = window.winfo_id()
                        
                        # SetWindowPos标志
                        SWP_NOACTIVATE = 0x0010
                        SWP_NOMOVE = 0x0002
                        SWP_NOSIZE = 0x0001
                        
                        # 将窗口置于顶层但不激活
                        ctypes.windll.user32.SetWindowPos(
                            hwnd, -1, 0, 0, 0, 0, 
                            SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE
                        )
                        
                        # 设置窗口样式为无激活
                        GWL_EXSTYLE = -20
                        WS_EX_NOACTIVATE = 0x08000000
                        WS_EX_TOOLWINDOW = 0x00000080
                        
                        style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)
                        ctypes.windll.user32.SetWindowLongW(
                            hwnd, GWL_EXSTYLE, 
                            style | WS_EX_NOACTIVATE | WS_EX_TOOLWINDOW
                        )
                    except Exception as e:
                        logger.debug(f"设置窗口不获取焦点失败: {e}")
            except Exception as e:
                logger.error(f"应用窗口样式失败: {e}")

    def show_progress_indicator(self):
        """显示进度提示"""
        if self.is_showing:
            logger.debug("GUI已在显示中")
            return
            
        try:
            # 创建新窗口
            self.progress_indicator = tk.Toplevel(self.root)
            self.progress_indicator.title("")
            
            # 移除标题栏和边框，使其成为纯提示窗口
            self.progress_indicator.overrideredirect(True)
            
            # 应用窗口样式
            self._apply_window_styling(self.progress_indicator)
            
            # 设置窗口大小和位置
            window_width = 170
            window_height = 26
            
            # 获取鼠标当前位置
            mouse_x, mouse_y = pyautogui.position()
            
            # 获取屏幕尺寸
            screen_width = self.root.winfo_screenwidth()
            screen_height = self.root.winfo_screenheight()
            
            # 计算窗口位置，默认显示在鼠标右下方
            x = mouse_x + 10  # 修改为与_update_position相同的偏移量
            y = mouse_y + 5   # 修改为与_update_position相同的偏移量
            
            # 如果窗口超出屏幕右边界，则显示在鼠标左侧
            if x + window_width > screen_width:
                x = mouse_x - window_width - 10  # 修改为与_update_position相同的偏移量
            
            # 如果窗口超出屏幕下边界，则显示在鼠标上方
            if y + window_height > screen_height:
                y = mouse_y - window_height - 5   # 修改为与_update_position相同的偏移量
            
            # 确保窗口完全在屏幕内
            x = max(0, min(x, screen_width - window_width))
            y = max(0, min(y, screen_height - window_height))
            
            # 设置窗口位置和大小
            self.progress_indicator.geometry(f"{window_width}x{window_height}+{x}+{y}")
                
            # 创建主框架 - 添加细边框和圆角效果
            main_frame = tk.Frame(
                self.progress_indicator, 
                bg=self.theme["background"], 
                bd=0,  # 去除内部边框
                relief=tk.FLAT,
                highlightbackground=self.theme["border"],
                highlightthickness=1
            )
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 创建单行布局框架，使用紧凑布局
            row_frame = tk.Frame(main_frame, bg=self.theme["background"])
            row_frame.pack(fill=tk.BOTH, expand=True)
            
            # 直接使用中文提示文本
            self.progress_label = tk.Label(
                row_frame, 
                text=self.progress_text, 
                bg=self.theme["background"], 
                fg=self.theme["text"],
                font=("微软雅黑", 9),
                padx=5,  # 使用padding代替固定宽度
                anchor="center"  # 居中对齐
            )
            self.progress_label.pack(side=tk.LEFT)
            
            # 进度条 - 调整比例
            self.progress_bar = ttk.Progressbar(
                row_frame, 
                orient="horizontal",
                length=100,  # 长一些，占据主要空间
                mode="determinate",
                style="Slim.Horizontal.TProgressbar"
            )
            self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(2, 5), pady=9)  # 垂直居中
            
            # 隐藏状态标签，简化界面
            self.status_label = tk.Label(
                row_frame,
                text="",
                bg=self.theme["background"],
                fg=self.theme["secondary_text"],
                font=("微软雅黑", 8)
            )
            # 不显示状态标签，使界面更简洁
            # self.status_label.pack(side=tk.LEFT, padx=(0, 5))
            
            # 初始化进度为0
            self.progress_bar["value"] = 0
            self.current_progress = 0
            
            # 设置默认目标进度为10%
            self._animate_progress_to(10)
            
            # 现在显示窗口
            self.progress_indicator.deiconify()
            
            # 设置定时器更新位置和动画
            self.progress_after_id = self.root.after(200, self._update_position)
            
            # 开始点动画
            self._start_dots_animation()
            
            # 设置标志
            self.is_showing = True
            
            logger.debug("显示进度指示器")
        except Exception as e:
            logger.error(f"显示进度指示器失败: {e}", exc_info=True)

    def _cleanup_resources(self):
        """清理所有资源和定时器"""
        # 清理动画计时器
        if self.dots_animation_id and self.root:
            try:
                self.root.after_cancel(self.dots_animation_id)
            except:
                pass
        self.dots_animation_id = None
            
        # 清理进度条动画
        if self.animation_id and self.root:
            try:
                self.root.after_cancel(self.animation_id)
            except:
                pass
        self.animation_id = None
            
        # 清理位置更新定时器
        if self.progress_after_id and self.root:
            try:
                self.root.after_cancel(self.progress_after_id)
            except:
                pass
        self.progress_after_id = None
            
        # 销毁窗口
        if self.progress_indicator is not None:
            try:
                # 检查窗口是否仍然有效
                if hasattr(self.progress_indicator, 'winfo_exists') and self.progress_indicator.winfo_exists():
                    self.progress_indicator.destroy()
                else:
                    logger.debug("进度提示窗口已不存在，无需销毁")
            except Exception as e:
                logger.error(f"销毁进度提示窗口时出错: {e}")
            finally:
                self.progress_indicator = None
                
        # 重置所有引用
        self.progress_indicator = None
        self.progress_label = None
        self.progress_bar = None
        self.status_label = None
        self.dots_count = 0

    def _animate_progress_to(self, target_percent):
        """平滑动画过渡到目标进度百分比"""
        self.target_progress = target_percent
        
        if self.animation_id:
            try:
                if self.root:
                    self.root.after_cancel(self.animation_id)
            except:
                pass
                
        def _update_progress_animation():
            if not self.is_showing or not self.progress_bar or not self.progress_indicator:
                return
                
            try:
                if self.current_progress < self.target_progress:
                    # 逐步增加进度
                    step = max(0.5, (self.target_progress - self.current_progress) / 10)
                    self.current_progress = min(self.current_progress + step, self.target_progress)
                    if self.progress_bar:
                        self.progress_bar["value"] = self.current_progress
                    
                    # 继续动画直到达到目标
                    if self.current_progress < self.target_progress and self.root:
                        self.animation_id = self.root.after(self.animation_speed, _update_progress_animation)
            except Exception as e:
                logger.error(f"进度动画更新错误: {e}")
                
        # 启动动画
        _update_progress_animation()

    def _reset_progress_animation(self):
        """重置进度条动画"""
        self.current_progress = 0
        self.target_progress = 0
        if self.progress_bar:
            self.progress_bar["value"] = 0
        self._animate_progress_to(10)  # 从0开始，先到10%

    def _start_dots_animation(self):
        """启动点点动画"""
        if self.dots_animation_id:
            try:
                if self.root:
                    self.root.after_cancel(self.dots_animation_id)
            except:
                pass
                
        def _animate_dots():
            try:
                if not self.is_showing or not self.progress_label:
                    return
                
                if not self.progress_label.winfo_exists():
                    return
                    
                # 获取当前文本和基础文本
                default_lang = "zh"
                base_text = self.progress_texts.get(default_lang, "翻译中")
                
                # 更紧凑的点动画方式，为水平布局优化
                dots = ["", ".", "..", "..."]
                self.dots_count = (self.dots_count + 1) % 4
                
                # 为单行布局显示文本
                new_text = f"{base_text}{dots[self.dots_count]}"
                
                # 只有当文本变化时才更新
                current_text = self.progress_label["text"]
                if current_text != new_text:
                    self.progress_label.config(text=new_text)
                
                # 使用更短的动画间隔使动画更流畅
                if self.root and self.is_showing:
                    self.dots_animation_id = self.root.after(300, _animate_dots)
            except Exception as e:
                logger.error(f"点动画更新错误: {e}")
                
        # 启动动画
        _animate_dots()

    def _update_position(self):
        """更新进度提示位置"""
        try:
            # 检查root和progress_indicator是否有效
            if (self.root is None or not self.root.winfo_exists() or 
                self.progress_indicator is None):
                logger.debug("无法更新位置：窗口已无效")
                self.progress_after_id = None
                return
                
            # 检查progress_indicator是否仍然存在
            try:
                if not self.progress_indicator.winfo_exists():
                    logger.debug("无法更新位置：提示窗口已关闭")
                    self.progress_after_id = None
                    return
                    
                # 更新位置，使其跟随鼠标但有一定偏移量，避免遮挡
                new_x, new_y = pyautogui.position()
                window_width = self.progress_indicator.winfo_width()
                window_height = self.progress_indicator.winfo_height()
                
                # 获取屏幕尺寸
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                
                # 优化位置逻辑 - 根据鼠标位置放置窗口在不同位置
                offset_x = 10  # 水平偏移量
                offset_y = 5  # 垂直偏移量
                
                # 默认在右下方显示
                pos_x = new_x + offset_x
                pos_y = new_y + offset_y
                
                # 如果靠近屏幕右边缘，则改为左侧显示
                if pos_x + window_width > screen_width:
                    pos_x = new_x - window_width - offset_x
                
                # 如果靠近屏幕下边缘，则改为上方显示
                if pos_y + window_height > screen_height:
                    pos_y = new_y - window_height - offset_y
                
                # 确保窗口始终在屏幕内
                pos_x = max(0, min(screen_width - window_width, pos_x))
                pos_y = max(0, min(screen_height - window_height, pos_y))
                
                self.progress_indicator.geometry(f"+{pos_x}+{pos_y}")
                
                # 安排下一次更新
                self.progress_after_id = self.root.after(200, self._update_position)
            except tk.TclError as e:
                logger.debug(f"更新位置时发生Tcl错误: {e}")
                self.progress_after_id = None
                # 如果发生Tcl错误，可能是窗口已被销毁，重置状态
                self.is_showing = False
                # Note: Calling _cleanup_resources() here might be problematic if it tries to destroy the window again.
                # Consider calling a safer cleanup method if needed.
                # self._cleanup_resources() # Temporarily commented out to avoid potential double-destroy issues
        except Exception as e:
            logger.error(f"更新GUI位置失败: {e}")
            # 确保出错时不会继续尝试更新
            self.progress_after_id = None

    def hide_progress_indicator(self):
        """隐藏进度提示"""
        try:
            # 停止所有动画
            self._reset_progress_animation()
            
            # 先清理定时器和动画
            self._cleanup_animations_and_timers()
            
            # 销毁窗口
            if self.progress_indicator is not None:
                try:
                    # 检查窗口是否仍然有效
                    if hasattr(self.progress_indicator, 'winfo_exists') and self.progress_indicator.winfo_exists():
                        self.progress_indicator.destroy()
                    else:
                        logger.debug("进度提示窗口已不存在，无需销毁")
                except Exception as e:
                    logger.error(f"销毁进度提示窗口时出错: {e}")
                finally:
                    self.progress_indicator = None
                    
            # 重置状态
            self.is_showing = False
            logger.debug("隐藏GUI等待提示")
        except Exception as e:
            logger.error(f"隐藏进度提示时出错: {e}")
            # 确保状态被重置，即使出现错误
            self.is_showing = False
            self.progress_indicator = None

    def _cleanup_animations_and_timers(self):
        """清理动画和计时器，但不销毁窗口"""
        try:
            # 停止动画和计时器
            if self.dots_animation_id is not None and self.root:
                try:
                    self.root.after_cancel(self.dots_animation_id)
                    self.dots_animation_id = None
                except Exception as e:
                    logger.debug(f"取消 dots 动画时出错: {e}")
                    self.dots_animation_id = None  # 确保即使出错也重置ID
                
            if self.animation_id is not None and self.root:
                try:
                    self.root.after_cancel(self.animation_id)
                    self.animation_id = None
                except Exception as e:
                    logger.debug(f"取消进度动画时出错: {e}")
                    self.animation_id = None
                
            if self.progress_after_id is not None and self.root:
                try:
                    self.root.after_cancel(self.progress_after_id)
                    self.progress_after_id = None
                except Exception as e:
                    logger.debug(f"取消 progress_after 计时器时出错: {e}")
                    self.progress_after_id = None
                
            # 重置动画状态
            self.dots_count = 0
            self.current_progress = 0
            self.target_progress = 0
        except Exception as e:
            logger.error(f"清理动画和计时器时出错: {e}")

    def update_progress_indicator(self, status_key: str, progress: int = None, custom_message: str = None):
        """更新进度提示状态
        
        Args:
            status_key: 状态键，对应status_texts中的键
            progress: 进度百分比，范围0-100
            custom_message: 自定义状态消息，如果提供则优先使用
        """
        if not self.is_showing or not self.progress_indicator:
            logger.debug("进度指示器未显示，无法更新状态")
            return
            
        try:
            # 更新状态文本
            status_text = custom_message or self.status_texts.get(status_key, "")
            if self.status_label and status_text:
                self.status_label.configure(text=status_text)
                
            # 更新翻译文本，根据状态选择合适的文本
            if status_key == "translating":
                self.progress_label.configure(text=self.progress_text)
            elif status_key == "success":
                self.progress_label.configure(text=self.status_texts["success"])
            elif status_key == "network_error" or status_key == "api_error":
                self.progress_label.configure(text=self.status_texts[status_key])
                
            # 更新进度
            if progress is not None:
                self._animate_progress_to(progress)
                
            # 刷新窗口位置
            self._update_position()
        except Exception as e:
            logger.error(f"更新进度指示器失败: {e}")
            # 在出现错误时尝试隐藏指示器，避免界面卡住
            try:
                self.hide_progress_indicator()
            except:
                pass

class KeyboardListener:
    def __init__(self, translator: Translator):
        self.translator = translator
        self.listener = keyboard.Listener(on_press=self.on_press, on_release=self.on_release)
        self.listener.daemon = True
        self.space_count = 0
        self.last_space_time = 0
        self.last_trigger_time = 0
        self.space_detection_timeout = 1.0  # 空格按键超过这个时间间隔重置计数
        self.trigger_cooldown = 2.0  # 两次触发之间的最小时间间隔
        
    def get_mouse_position(self):
        """获取鼠标位置
        
        Returns:
            tuple: 鼠标坐标(x, y)，如果获取失败则返回None
        """
        try:
            import pyautogui
            return pyautogui.position()
        except Exception as e:
            logger.error(f"获取鼠标位置失败: {e}")
            return None

    def check_cancel_shortcut(self, key):
        """检查是否按下了取消翻译的快捷键
        
        Args:
            key: 按下的键
            
        Returns:
            bool: 总是返回False，因为已移除取消功能
        """
        return False

    def on_press(self, key):
        """处理键盘按键事件"""
        try:
            # 如果翻译器不可用，忽略键盘事件
            if not self.translator or not hasattr(self.translator, 'config'):
                return
            # 如果翻译器正在进行网络请求，且配置为忽略键盘事件，则拦截
            if getattr(self.translator, 'suppress_keyboard', False):
                return 

            # 只处理空格键
            if key != keyboard.Key.space:
                self.space_count = 0
                return
            # 获取鼠标坐标
            mouse_pos = self.get_mouse_position()
            # 如果获取鼠标位置失败或者鼠标在屏幕顶部区域，忽略触发
            if mouse_pos is None or mouse_pos[1] < 50:
                return
            # 计算空格按键时间间隔
            current_time = time.time()
            space_interval = current_time - self.last_space_time
            # 如果间隔过长，重置计数
            if space_interval > self.space_detection_timeout:
                self.space_count = 1
            else:
                self.space_count += 1
            self.last_space_time = current_time
            # 检查冷却时间，防止频繁触发
            if current_time - self.last_trigger_time < self.trigger_cooldown:
                return
            # 检测到三次空格，触发翻译
            if self.space_count >= 3:
                self.space_count = 0
                self.last_trigger_time = current_time
                # 检查翻译器状态
                if self.translator.config.translation_mode == 0:
                    logger.warning("请先选择翻译模式")
                    return
                if self.translator.in_progress:
                    logger.info("已有翻译任务进行中，请稍候...")
                    return
                logger.info("检测到三次空格，触发翻译")
                asyncio.run_coroutine_threadsafe(self.translator.replacement_translation(), self.translator.loop)
        except Exception as e:
            logger.error(f"键盘事件处理错误: {e}")

    def on_release(self, key):
        """处理键盘释放事件"""
        pass  # 不需要处理键盘释放事件
    
    def start(self):
        """启动键盘监听"""
        self.listener.start()

SETTINGS_MENU_ITEMS = {
    1: {"desc": "修改API密钥", "func": lambda t: modify_api_key(t)},
    2: {"desc": "修改模型ID", "func": lambda t: modify_model_id(t)},
    3: {"desc": "修改API模式（1: 谷歌API, 2: OpenAI兼容API）", "func": lambda t: modify_api_mode(t)},
    4: {"desc": "修改API Base URL", "func": lambda t: modify_api_base_url(t)},
    5: {"desc": "修改API Endpoint", "func": lambda t: modify_api_endpoint(t)},
    6: {"desc": "修改模型温度（建议0-2）", "func": lambda t: modify_temperature(t)},
    7: {"desc": "修改Top-P核采样（建议0-1）", "func": lambda t: modify_top_p(t)},
    8: {"desc": "修改默认翻译模式", "func": lambda t: modify_translation_mode(t)},
    9: {"desc": "添加自定义翻译模式", "func": lambda t: add_custom_translation_mode(t)},
    10: {"desc": "删除翻译模式", "func": lambda t: delete_translation_mode(t)},
    11: {"desc": "修改最大翻译文本字数", "func": lambda t: modify_max_text_length(t)},
    12: {"desc": "修改最大上下文数量（建议0-20）", "func": lambda t: modify_context_max_count(t)},
    13: {"desc": "开启/关闭调试模式", "func": lambda t: toggle_debug_mode(t)},
    14: {"desc": "修改请求最小间隔", "func": lambda t: modify_request_min_interval(t)},
    15: {"desc": "修改日志最大条目数", "func": lambda t: modify_log_max_entries(t)},
    16: {"desc": "开启/关闭GUI等待提示", "func": lambda t: toggle_show_gui_progress(t)},
    17: {"desc": "修改语言族配置", "func": lambda t: modify_language_families(t)},
    18: {"desc": "修改语言检测缓存大小", "func": lambda t: modify_language_cache_size(t)},
    19: {"desc": "修改翻译结果相似度阈值", "func": lambda t: modify_same_language_threshold(t)},
}

def validate_input(prompt: str, valid_options: list = [], type_cast: Callable = str, range_check: tuple = (), allow_empty: bool = False) -> Any:
    """验证用户输入"""
    while True:
        value = input(prompt).strip()
        if value.lower() == "返回":
            return "返回"
        if not value and not allow_empty:
            print("输入不能为空，请重新输入。")
            continue
        try:
            casted_value = type_cast(value)
            if valid_options and casted_value not in valid_options:
                print(f"无效输入，请输入以下选项之一：{valid_options}")
                continue
            if range_check and (casted_value < range_check[0] or casted_value > range_check[1]):
                print(f"输入超出范围，请输入 {range_check[0]} 到 {range_check[1]} 之间的值。")
                continue
            return casted_value
        except ValueError:
            print(f"请输入有效的 {type_cast.__name__} 类型值。")
            continue

def display_menu(menu_items: Dict, title: str, current_mode: int = -1) -> None:
    """显示主菜单"""
    print(f"\n{title}：")
    translation_modes = {k: v for k, v in menu_items.items() if k not in [0, "00"]}
    settings_item = menu_items.get(0, {"desc": "设置"})
    clear_context_item = menu_items.get("00", {"desc": "清空上下文"})

    for key, item in sorted(translation_modes.items(), key=lambda x: int(x[0])):
        mark = "[√]" if key == current_mode else ""
        print(f"  {key}. {item['desc']} {mark}")

    print(f"  0. {settings_item['desc']}")
    print(f"  00. {clear_context_item['desc']}")

def display_settings_menu(menu_items: Dict, title: str, config: Config, translator: Translator) -> None:
    """显示设置菜单"""
    print(f"\n{title}：")
    for key, item in menu_items.items():
        extra_info = ""
        if key == 1:
            extra_info = f"（当前：{'已设置' if config.api_key else '未设置'}）"
        elif key == 2:
            extra_info = f"（当前：{config.model_id}）"
        elif key == 3:
            extra_info = f"（当前：{config.api_mode}）"
        elif key == 4:
            extra_info = f"（当前：{config.api_base_url}）"
        elif key == 5:
            extra_info = f"（当前：{config.api_endpoint}）"
        elif key == 6:
            extra_info = f"（当前：{config.temperature}）"
        elif key == 7:
            extra_info = f"（当前：{config.top_p}）"
        elif key == 8:
            mode_map = {k: v["desc"] for k, v in translator.mode_menu_items.items() if k not in [0, "00"]}
            extra_info = f"（当前：{mode_map.get(config.translation_mode, '未知')}）"
        elif key == 11:
            extra_info = f"（当前：{config.max_text_length}）"
        elif key == 12:
            extra_info = f"（当前：{config.context_max_count}）"
        elif key == 13:
            extra_info = f"（当前：{'开启' if config.debug_mode else '关闭'}）"
        elif key == 14:
            extra_info = f"（当前：{config.request_min_interval} 秒）"
        elif key == 15:
            extra_info = f"（当前：INFO {config.log_info_max}，其他 {config.log_other_max}）"
        elif key == 16:
            extra_info = f"（当前：{'开启' if config.show_gui_progress else '关闭'}）"
        elif key == 17:
            # 语言族配置显示
            language_families = getattr(config, 'language_families', None) or {}
            family_count = len(language_families)
            if family_count == 0:
                extra_info = "（当前：未配置）"
            else:
                extra_info = f"（当前：已配置{family_count}个语言族）"
        elif key == 18:
            # 语言检测缓存大小显示
            cache_size = getattr(config, 'language_detection_cache_size', 100)
            extra_info = f"（当前：{cache_size} 条）"
        elif key == 19:
            # 翻译结果相似度阈值显示
            threshold = getattr(config, 'same_language_match_threshold', 0.5)
            extra_info = f"（当前：{threshold:.2f}）"
        print(f"  {key}. {item['desc']} {extra_info}")
    print("请选择（输入数字或'返回'返回主菜单）：")

def modify_api_key(translator: Translator) -> None:
    """修改API密钥配置"""
    config = translator.config
    extra_info = f"（当前：{'已设置' if config.api_key else '未设置'}）"
    print(f"修改API密钥 {extra_info}")
    print("注意：本程序只接受加密后的API密钥，请使用api_crypto.py工具进行加密。")
    print("请输入新的加密后的API密钥（直接回车保持不变）：")
    
    global api_crypto, decrypted_api_key
    new_value = input().strip()
    if not new_value:
        print("保持原值不变")
        return
    
    # 初始化API加密工具
    if not api_crypto:
        init_api_crypto()
    
    # 检查是否是加密后的API密钥
    if not api_crypto.is_encrypted(new_value):
        logger.error("输入的不是有效的加密API密钥，请使用api_crypto.py工具进行加密。")
        return
    
    # 解密测试API密钥是否有效
    decrypted_key = api_crypto.decrypt(new_value)
    if not decrypted_key:
        logger.error("加密API密钥无效或无法解密")
        return
    
    # 更新全局解密后的API密钥
    decrypted_api_key = decrypted_key
    
    # 保存加密后的API密钥到配置文件
    config.api_key = new_value
    save_config(config)
    logger.info(f"API密钥已更新（已加密并保存到配置文件）")

def modify_model_id(translator: Translator) -> None:
    """修改模型ID"""
    print("\n设置 > 修改模型ID：")
    print(f"当前值：{translator.config.model_id}")
    new_value = validate_input("请输入新的模型ID（输入'返回'以取消）：")
    if new_value != "返回":
        translator.config.model_id = new_value
        save_config(translator.config)
        logger.info(f"模型ID已更新为：{new_value}")
    else:
        print("操作取消，返回设置菜单。")

def modify_api_mode(translator: Translator) -> None:
    """修改API模式"""
    print("\n设置 > 修改API模式：")
    print(f"当前值：{translator.config.api_mode}")
    new_value = validate_input("请选择API模式（1: 谷歌API, 2: OpenAI兼容API，输入'返回'以取消）：", valid_options=[1, 2], type_cast=int)
    if new_value != "返回":
        translator.config.api_mode = "gemini" if new_value == 1 else "openai"
        save_config(translator.config)
        logger.info(f"API模式已更新为：{'谷歌API (gemini)' if new_value == 1 else 'OpenAI兼容API (openai)'}")
    else:
        print("操作取消，返回设置菜单。")

def modify_api_base_url(translator: Translator) -> None:
    """修改API Base URL"""
    print("\n设置 > 修改API Base URL：")
    print(f"当前值：{translator.config.api_base_url}")
    new_value = validate_input("请输入新的API Base URL（例如 https://api.openai.com，输入'返回'以取消）：")
    if new_value != "返回":
        translator.config.api_base_url = new_value
        save_config(translator.config)
        logger.info(f"API Base URL已更新为：{new_value}")
    else:
        print("操作取消，返回设置菜单。")

def modify_api_endpoint(translator: Translator) -> None:
    """修改 API Endpoint"""
    print("\n设置 > 修改API Endpoint：")
    print(f"当前值：{translator.config.api_endpoint}")
    new_value = validate_input("请输入新的API Endpoint（例如 /v1/chat/completions，输入'返回'以取消）：")
    if new_value != "返回":
        translator.config.api_endpoint = new_value
        save_config(translator.config)
        logger.info(f"API Endpoint已更新为：{new_value}")
    else:
        print("操作取消，返回设置菜单。")

def modify_temperature(translator: Translator) -> None:
    """修改模型温度"""
    modify_setting(
        translator=translator,
        setting_name="temperature",
        prompt="模型温度",
        type_cast=float,
        range_check=(0, 2)
    )

def modify_top_p(translator: Translator) -> None:
    """修改 Top-P 值"""
    modify_setting(
        translator=translator,
        setting_name="top_p",
        prompt="Top-P核采样",
        type_cast=float,
        range_check=(0, 1)
    )

def modify_translation_mode(translator: Translator) -> None:
    """修改默认翻译模式"""
    print("\n设置 > 修改默认翻译模式：")
    mode_map = {k: v["desc"] for k, v in translator.mode_menu_items.items() if k not in [0, "00"]}
    print(f"当前值：{mode_map.get(translator.config.translation_mode, '未知')}")
    valid_modes = [k for k in translator.mode_menu_items.keys() if k not in [0, "00"]]
    new_value = validate_input(f"请输入新的默认翻译模式（{min(valid_modes)}-{max(valid_modes)}，输入'返回'以取消）：", valid_options=valid_modes, type_cast=int)
    if new_value != "返回":
        translator.config.translation_mode = new_value
        save_config(translator.config)
        logger.info(f"默认翻译模式已更新为：{mode_map[new_value]}")
    else:
        print("操作取消，返回设置菜单。")

def modify_max_text_length(translator: Translator) -> None:
    """修改最大翻译文本字数"""
    modify_setting(
        translator=translator,
        setting_name="max_text_length",
        prompt="最大翻译文本字数",
        type_cast=int,
        range_check=(1, float('inf'))
    )

def modify_context_max_count(translator: Translator) -> None:
    """修改最大上下文数量"""
    modify_setting(
        translator=translator,
        setting_name="context_max_count",
        prompt="最大上下文数量",
        type_cast=int,
        range_check=(0, 20)
    )



def toggle_debug_mode(translator: Translator) -> None:
    """切换调试模式"""
    print("\n设置 > 开启/关闭调试模式：")
    print(f"当前值：{'开启' if translator.config.debug_mode else '关闭'}")
    new_value = validate_input("请输入'开启'或'关闭'（输入'返回'以取消）：", valid_options=["开启", "关闭"])
    if new_value == "开启":
        translator.config.debug_mode = True
        logger.setLevel(logging.DEBUG)
        console_handler.setLevel(logging.DEBUG)
        file_handler.setLevel(logging.DEBUG)
        save_config(translator.config)
        logger.info("调试模式已开启")
    elif new_value == "关闭":
        translator.config.debug_mode = False
        logger.setLevel(logging.INFO)
        console_handler.setLevel(logging.INFO)
        file_handler.setLevel(logging.INFO)
        save_config(translator.config)
        logger.info("调试模式已关闭")
    else:
        print("操作取消，返回设置菜单。")

def modify_request_min_interval(translator: Translator) -> None:
    """修改请求最小间隔"""
    modify_setting(
        translator=translator,
        setting_name="request_min_interval",
        prompt="请求最小间隔",
        type_cast=float,
        range_check=(0.5, 2.0),
        pre_display_func=lambda x: f"{x} 秒"
    )

def modify_log_max_entries(translator: Translator) -> None:
    """修改日志最大条目数"""
    print("\n设置 > 修改日志最大条目数：")
    print(f"当前值：INFO {translator.config.log_info_max}，其他 {translator.config.log_other_max}")
    info_max = validate_input("请输入新的 INFO 日志最大条目数（建议50-200，输入'返回'以取消）：", type_cast=int, range_check=(50, 200))
    if info_max == "返回":
        print("操作取消，返回设置菜单。")
        return
    other_max = validate_input("请输入新的非 INFO 日志最大条目数（建议10-50，输入'返回'以取消）：", type_cast=int, range_check=(10, 50))
    if other_max == "返回":
        print("操作取消，返回设置菜单。")
        return
    translator.config.log_info_max = info_max
    translator.config.log_other_max = other_max
    file_handler.info_max = info_max
    file_handler.other_max = other_max
    save_config(translator.config)
    logger.info(f"日志最大条目数已更新为：INFO {info_max}，其他 {other_max}")

def toggle_show_gui_progress(translator: Translator) -> None:
    """切换 GUI 等待提示"""
    print("\n设置 > 开启/关闭GUI等待提示：")
    print(f"当前值：{'开启' if translator.config.show_gui_progress else '关闭'}")
    new_value = validate_input("请输入'开启'或'关闭'（输入'返回'以取消）：", valid_options=["开启", "关闭"])
    if new_value == "开启":
        translator.config.show_gui_progress = True
        save_config(translator.config)
        logger.info("GUI等待提示已开启")
    elif new_value == "关闭":
        translator.config.show_gui_progress = False
        save_config(translator.config)
        logger.info("GUI等待提示已关闭")
    else:
        print("操作取消，返回设置菜单。")

def add_custom_translation_mode(translator: Translator) -> None:
    """添加自定义翻译模式"""
    print("\n设置 > 添加自定义翻译模式：")
    source_lang = validate_input("请输入源语言名称（如'中文'）：")
    if source_lang == "返回":
        print("操作取消，返回设置菜单。")
        return
    source_code = validate_input("请输入源语言代码（ISO 639-1，如'zh'）：")
    if source_code == "返回":
        print("操作取消，返回设置菜单。")
        return
    target_lang = validate_input("请输入目标语言名称（如'英文'）：")
    if target_lang == "返回":
        print("操作取消，返回设置菜单。")
        return
    target_code = validate_input("请输入目标语言代码（ISO 639-1，如'en'）：")
    if target_code == "返回":
        print("操作取消，返回设置菜单。")
        return
    style = validate_input("请输入语气风格（可选，如'敬语'，按回车跳过）：", allow_empty=True) or ""
    default_lang = validate_input("请输入默认语言名称（通常为源语言，如'中文'）：", allow_empty=True) or source_lang

    # 检查语言代码是否已存在于系统中
    is_new_source = source_code not in translator.mode_config["tone_particles"]
    is_new_target = target_code not in translator.mode_config["tone_particles"]
    
    # 询问新语言的语气词配置
    if is_new_source:
        source_tone_pattern = validate_input(f"请输入{source_lang}({source_code})的语气词正则表达式（可选，按回车使用默认值）：", allow_empty=True)
        if source_tone_pattern:
            translator.mode_config["tone_particles"][source_code] = source_tone_pattern
            logger.info(f"已添加{source_lang}({source_code})的自定义语气词: {source_tone_pattern}")
    
    if is_new_target:
        target_tone_pattern = validate_input(f"请输入{target_lang}({target_code})的语气词正则表达式（可选，按回车使用默认值）：", allow_empty=True)
        if target_tone_pattern:
            translator.mode_config["tone_particles"][target_code] = target_tone_pattern
            logger.info(f"已添加{target_lang}({target_code})的自定义语气词: {target_tone_pattern}")

    # 创建新的翻译模式
    new_mode_id = max([k for k in translator.mode_config["translation_modes"].keys() if isinstance(k, int)]) + 1
    translator.mode_config["translation_modes"][new_mode_id] = {
        "source_lang": source_lang,
        "target_lang": target_lang,
        "style": style,
        "default_lang": default_lang,
        "source_code": source_code,
        "target_code": target_code
    }

    translator.mode_menu_items[new_mode_id] = {"desc": f"{source_lang}-{target_lang}" + (f"-{style}" if style else "")}
    translator.history[new_mode_id] = []
    
    # 补全新增语言的特征和语气词
    translator.mode_config = complete_language_features_and_tones(translator.mode_config)
    save_mode_config(translator.mode_config)
    logger.info(f"已添加自定义翻译模式 {new_mode_id}: {source_lang} ({source_code}) -> {target_lang} ({target_code})，风格：{style or '无'}")

def delete_translation_mode(translator: Translator) -> None:
    """删除翻译模式"""
    print("\n设置 > 删除翻译模式：")
    if len([k for k in translator.mode_config["translation_modes"].keys() if isinstance(k, int)]) <= 1:
        logger.warning("至少保留一个翻译模式，无法删除")
        return

    mode_map = {k: v["desc"] for k, v in translator.mode_menu_items.items() if k not in [0, "00"]}
    for key, desc in mode_map.items():
        print(f"  {key}. {desc}")

    valid_modes = [k for k in mode_map.keys()]
    mode_to_delete = validate_input(f"请输入要删除的模式编号（{min(valid_modes)}-{max(valid_modes)}，输入'返回'以取消）：", valid_options=valid_modes, type_cast=int)
    if mode_to_delete == "返回":
        print("操作取消，返回设置菜单。")
        return

    if mode_to_delete == translator.config.translation_mode:
        logger.warning("无法删除当前默认翻译模式，请先切换默认模式")
        return

    del translator.mode_config["translation_modes"][mode_to_delete]
    del translator.mode_menu_items[mode_to_delete]
    if mode_to_delete in translator.history:
        del translator.history[mode_to_delete]
    save_mode_config(translator.mode_config)
    logger.info(f"已删除翻译模式 {mode_to_delete}: {mode_map[mode_to_delete]}")

def enter_settings_menu(translator: Translator) -> None:
    """进入设置菜单"""
    while True:
        display_settings_menu(SETTINGS_MENU_ITEMS, "设置", translator.config, translator)
        choice = input().strip()
        if choice.lower() == "返回":
            break
        try:
            choice_int = int(choice)
            if choice_int not in SETTINGS_MENU_ITEMS:
                print("无效选项，请输入菜单中的数字或'返回'返回主菜单。")
                continue
            SETTINGS_MENU_ITEMS[choice_int]["func"](translator)
        except ValueError:
            print("请输入有效的数字选项，或输入'返回'返回主菜单。")

gui_handler = None

def main():
    """主函数"""
    # 检查cryptography库
    try:
        import cryptography
        logger.debug(f"已加载cryptography库版本: {cryptography.__version__}")
    except ImportError:
        print("错误: 未找到cryptography库，请先安装:")
        print("pip install cryptography")
        sys.exit(1)
    
    # 加载配置
    config = Config(**load_config())

    # 创建tkinter根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建GUI处理器和翻译器
    global gui_handler
    gui_handler = GUIHandler(root)
    translator = Translator(config, root)
    
    # 创建键盘监听器并设置为守护线程
    keyboard_listener = KeyboardListener(translator)
    keyboard_listener.start()
    
    # 全局退出标志
    global exiting
    exiting = False
    
    # 处理优雅退出
    def graceful_shutdown():
        """确保程序优雅退出，关闭所有资源"""
        global exiting
        if exiting:
            return
        exiting = True
        
        logger.info("正在关闭翻译程序...")
        try:
            # 关闭键盘监听器
            if keyboard_listener and keyboard_listener.listener and keyboard_listener.listener.running:
                keyboard_listener.listener.stop()
                
            # 关闭翻译器
            if translator:
                translator.shutdown()
            
            # 关闭GUI
            if gui_handler and gui_handler.is_showing:
                # 如果GUI正在显示，确保它被正确关闭
                root.after(0, gui_handler._cleanup_resources)
                
            # 关闭日志处理器
            for handler in logger.handlers[:]:
                try:
                    handler.close()
                    logger.removeHandler(handler)
                except Exception as e:
                    print(f"关闭日志处理器时出错: {e}", file=sys.stderr)
            
            # 手动触发垃圾回收
            logger.debug("执行垃圾回收...")
            gc.collect()

            # 确保程序完全退出
            root.destroy()
            logger.debug("资源释放完毕，程序退出")
            # 强制退出以防其他线程阻止退出
            os._exit(0)
        except Exception as e:
            print(f"关闭时发生错误: {e}", file=sys.stderr)
            os._exit(1)
    
    # 注册退出处理
    root.protocol("WM_DELETE_WINDOW", graceful_shutdown)
    
    # 设置信号处理器用于命令行中断
    try:
        import signal
        def signal_handler(sig, frame):
            logger.info(f"接收到信号 {sig}，准备优雅退出")
            graceful_shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    except (ImportError, AttributeError) as e:
        logger.warning(f"无法设置信号处理器: {e}")
    except Exception as e:
        logger.warning(f"设置信号处理器时出现未知错误: {e}")

    def run_console():
        """运行控制台界面"""
        try:
            while not exiting:
                display_menu(translator.mode_menu_items, "翻译模式选择", config.translation_mode)
                try:
                    choice = input("请选择翻译模式（输入数字切换模式，0进入设置，00清空上下文和翻译缓存，q退出）：").strip()
                    if choice.lower() in ['q', 'quit', 'exit']:
                        logger.info("用户请求退出程序")
                        graceful_shutdown()
                        break
                        
                    if choice == "0":
                        enter_settings_menu(translator)
                    elif choice == "00":
                        translator.clear_all_context()
                        print("已清空所有上下文和翻译缓存")
                    else:
                        choice_int = int(choice)
                        valid_modes = [k for k in translator.mode_menu_items.keys() if k not in [0, "00"]]
                        if choice_int not in valid_modes:
                            print(f"无效模式，请输入 {min(valid_modes)}-{max(valid_modes)} 之间的数字，或 0/00。")
                            continue
                        config.translation_mode = choice_int
                        save_config(config)
                        logger.info(f"已切换到翻译模式 {choice_int}: {translator.mode_menu_items[choice_int]['desc']}")
                except ValueError:
                    print("请输入有效的数字（如 1, 0, 00），或检查输入是否有误。")
                except EOFError:
                    logger.info("检测到EOF，退出控制台")
                    graceful_shutdown()
                    break
                time.sleep(0.1)
                # 在控制台循环的末尾尝试垃圾回收
                gc.collect()
        except Exception as e:
            logger.error(f"控制台线程异常: {e}")
            graceful_shutdown()

    # 启动控制台线程
    console_thread = threading.Thread(target=run_console, daemon=True)
    console_thread.start()

    # 检查网络和API状态并显示初始化消息
    is_network_ok = translator.service_manager.is_network_connected()
    if is_network_ok:
        # 异步检查API健康状态
        asyncio.run_coroutine_threadsafe(translator.check_api_health(), translator.loop)
        
        # 如果已经获取到API健康状态，显示相应消息
        if translator.api_health_status["healthy"] is not None:
            if translator.api_health_status["healthy"]:
                status_msg = "API服务正常"
            else:
                status_msg = f"API服务异常: {translator.api_health_status['message']}"
            logger.info(status_msg)
    else:
        logger.warning("网络连接不可用，可能会影响翻译功能")

    logger.info("翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式")
    
    try:
        root.mainloop()
    except Exception as e:
        logger.error(f"主循环异常: {e}")
    finally:
        graceful_shutdown()

# 添加一个辅助函数来检测操作系统，在提示文本中可能需要区分不同操作系统的快捷键
def get_os_type():
    """获取操作系统类型"""
    if sys.platform.startswith('win'):
        return 'windows'
    elif sys.platform.startswith('darwin'):
        return 'macos'
    elif sys.platform.startswith('linux'):
        return 'linux'
    else:
        return 'unknown'

def modify_setting(translator: Translator, setting_name: str, 
                  prompt: str, validation_func: Callable = None,
                  type_cast: Callable = str, range_check: tuple = None,
                  allow_empty: bool = False, valid_options: list = None,
                  transform_func: Callable = None,
                  pre_display_func: Callable = None) -> None:
    """通用设置修改函数，替代多个单独的modify_X函数
    
    Args:
        translator: 翻译器实例
        setting_name: 配置中的设置名称
        prompt: 显示给用户的提示文本
        validation_func: 自定义验证函数，返回(bool, str)表示(是否有效, 错误消息)
        type_cast: 类型转换函数
        range_check: 数值范围检查(min, max)
        allow_empty: 是否允许空输入
        valid_options: 有效的输入选项列表
        transform_func: 输入值转换函数，在保存前处理输入值
        pre_display_func: 显示当前值的自定义处理函数
    """
    config = translator.config
    current_value = getattr(config, setting_name)
    
    print(f"\n设置 > {prompt}：")
    
    # 显示当前值，可能应用自定义显示处理
    if pre_display_func:
        display_value = pre_display_func(current_value)
    else:
        display_value = current_value
    print(f"当前值：{display_value}")
    
    # 如果未提供valid_options，使用空列表
    valid_options = valid_options or []
    
    new_value = validate_input(
        f"请输入新的{prompt}，输入'返回'以取消: ",
        valid_options=valid_options, 
        type_cast=type_cast, 
        range_check=range_check, 
        allow_empty=allow_empty
    )
    
    if new_value != "返回":
        # 应用自定义验证
        if validation_func:
            is_valid, error_msg = validation_func(new_value)
            if not is_valid:
                print(error_msg)
                return
        
        # 应用变换函数
        if transform_func:
            new_value = transform_func(new_value)
            
        # 设置新值，如果新值与当前值不同并且不是空输入
        if new_value != "" and new_value != current_value:
            setattr(config, setting_name, new_value)
            save_config(config)
            logger.info(f"{prompt}已更新为: {new_value}")
        else:
            print(f"{prompt}保持不变")
    else:
        print("操作取消，返回设置菜单。")

def modify_language_families(translator: Translator) -> None:
    """修改语言族配置"""
    print("\n设置 > 修改语言族配置：")
    
    # 显示当前语言族配置
    current_families = translator.config.language_families or {}
    print("当前语言族配置：")
    for family, langs in current_families.items():
        print(f"  {family}: {', '.join(langs)}")
    
    # 操作选项
    print("\n操作选项：")
    print("  1. 添加新的语言族")
    print("  2. 修改现有语言族")
    print("  3. 删除语言族")
    
    action = validate_input("请选择操作（输入'返回'以取消）：", valid_options=[1, 2, 3], type_cast=int)
    if action == "返回":
        print("操作取消，返回设置菜单。")
        return
    
    # 添加新的语言族
    if action == 1:
        family_name = validate_input("请输入新语言族名称（如'cjk'，'european'等）：")
        if family_name == "返回":
            print("操作取消，返回设置菜单。")
            return
            
        if family_name in current_families:
            print(f"语言族 '{family_name}' 已存在，请选择其他名称或使用修改选项。")
            return
            
        langs_input = validate_input("请输入该语言族包含的语言代码，以逗号分隔（如'zh,ja,ko'）：")
        if langs_input == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        langs = [lang.strip() for lang in langs_input.split(",") if lang.strip()]
        if not langs:
            print("语言列表不能为空，操作取消。")
            return
            
        # 更新配置
        new_families = dict(current_families)
        new_families[family_name] = langs
        translator.config.language_families = new_families
        save_config(translator.config)
        logger.info(f"已添加新语言族 '{family_name}': {langs}")
    
    # 修改现有语言族
    elif action == 2:
        if not current_families:
            print("当前没有语言族配置，请先添加。")
            return
            
        family_names = list(current_families.keys())
        print("现有语言族：")
        for i, name in enumerate(family_names, 1):
            print(f"  {i}. {name}: {', '.join(current_families[name])}")
        
        family_index = validate_input("请选择要修改的语言族编号：", 
                                      valid_options=list(range(1, len(family_names)+1)), 
                                      type_cast=int)
        if family_index == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        family_name = family_names[family_index-1]
        current_langs = current_families[family_name]
        
        print(f"当前 '{family_name}' 包含语言: {', '.join(current_langs)}")
        langs_input = validate_input(f"请输入修改后的语言代码列表，以逗号分隔：")
        if langs_input == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        langs = [lang.strip() for lang in langs_input.split(",") if lang.strip()]
        if not langs:
            print("语言列表不能为空，操作取消。")
            return
            
        # 更新配置
        new_families = dict(current_families)
        new_families[family_name] = langs
        translator.config.language_families = new_families
        save_config(translator.config)
        logger.info(f"已更新语言族 '{family_name}': {langs}")
    
    # 删除语言族
    elif action == 3:
        if not current_families:
            print("当前没有语言族配置，无需删除。")
            return
            
        family_names = list(current_families.keys())
        print("现有语言族：")
        for i, name in enumerate(family_names, 1):
            print(f"  {i}. {name}: {', '.join(current_families[name])}")
        
        family_index = validate_input("请选择要删除的语言族编号：", 
                                      valid_options=list(range(1, len(family_names)+1)), 
                                      type_cast=int)
        if family_index == "返回":
            print("操作取消，返回设置菜单。")
            return
        
        family_name = family_names[family_index-1]
        confirm = validate_input(f"确定要删除语言族 '{family_name}'? (是/否)：", valid_options=["是", "否"])
        if confirm != "是":
            print("操作取消，返回设置菜单。")
            return
            
        # 更新配置
        new_families = dict(current_families)
        del new_families[family_name]
        translator.config.language_families = new_families
        save_config(translator.config)
        logger.info(f"已删除语言族 '{family_name}'")

def modify_language_cache_size(translator: Translator) -> None:
    """修改语言检测缓存大小"""
    modify_setting(
        translator=translator,
        setting_name="language_detection_cache_size",
        prompt="语言检测缓存大小",
        type_cast=int,
        range_check=(1, 1000),
        pre_display_func=lambda x: f"{x} 条"
    )
    # 如果修改了缓存大小，重新初始化缓存
    global language_detection_cache
    if language_detection_cache is not None:
        old_size = len(language_detection_cache)
        new_size = translator.config.language_detection_cache_size
        language_detection_cache = LRUCache(new_size)
        logger.info(f"已重置语言检测缓存，大小从 {old_size} 更改为 {new_size}")

def modify_same_language_threshold(translator: Translator) -> None:
    """修改翻译结果相似度阈值"""
    modify_setting(
        translator=translator,
        setting_name="same_language_match_threshold",
        prompt="翻译结果相似度阈值",
        type_cast=float,
        range_check=(0, 1),
        pre_display_func=lambda x: f"{x:.2f}"
    )

if __name__ == "__main__":
    main()