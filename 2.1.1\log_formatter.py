"""
日志格式化模块 - 优化控制台日志输出显示效果

该模块提供了一个函数setup_log_formatter，用于优化控制台日志输出的显示效果。
主要功能包括：
1. 彩色日志级别显示
2. 模块名称显示
3. 关键词高亮
4. 消息长度限制

使用方法：
在主程序中导入该模块，并在配置加载后调用setup_log_formatter函数：

```python
from log_formatter import setup_log_formatter

# 加载配置
config = Config(**load_config())

# 应用日志格式化器
setup_log_formatter(config)
```

或者在main函数中添加：

```python
def main():
    # 加载配置
    config = Config(**load_config())

    # 应用日志格式化器
    setup_log_formatter(config)
    logger.info("已应用自定义日志格式，优化显示效果")

    # 其他代码...
```
"""

import logging
import sys
import os
from typing import Optional, Dict, Any

# 导入并初始化 colorama 以支持 Windows 控制台颜色输出
try:
    import colorama
    # 在打包后的环境中强制初始化 colorama
    colorama.init(autoreset=True, convert=True, strip=False)
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

def _setup_console_encoding():
    """设置控制台编码为 UTF-8，确保中文字符正确显示"""
    try:
        # 设置标准输出编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        elif hasattr(sys.stdout, 'buffer'):
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

        # 设置标准错误输出编码
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
        elif hasattr(sys.stderr, 'buffer'):
            import io
            sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

    except Exception as e:
        # 如果编码设置失败，记录错误但不中断程序
        print(f"Warning: Failed to set console encoding: {e}", file=sys.stderr)

def _init_windows_console():
    """在 Windows 系统上初始化控制台以支持 ANSI 颜色代码"""
    if sys.platform.startswith('win'):
        try:
            # 尝试启用 Windows 控制台的 ANSI 支持
            import ctypes
            from ctypes import wintypes

            # 获取控制台句柄
            kernel32 = ctypes.windll.kernel32
            STD_OUTPUT_HANDLE = -11
            STD_ERROR_HANDLE = -12
            ENABLE_VIRTUAL_TERMINAL_PROCESSING = 0x0004

            # 启用标准输出的虚拟终端处理
            stdout_handle = kernel32.GetStdHandle(STD_OUTPUT_HANDLE)
            if stdout_handle != -1:
                mode = wintypes.DWORD()
                if kernel32.GetConsoleMode(stdout_handle, ctypes.byref(mode)):
                    mode.value |= ENABLE_VIRTUAL_TERMINAL_PROCESSING
                    kernel32.SetConsoleMode(stdout_handle, mode)

            # 启用标准错误输出的虚拟终端处理
            stderr_handle = kernel32.GetStdHandle(STD_ERROR_HANDLE)
            if stderr_handle != -1:
                mode = wintypes.DWORD()
                if kernel32.GetConsoleMode(stderr_handle, ctypes.byref(mode)):
                    mode.value |= ENABLE_VIRTUAL_TERMINAL_PROCESSING
                    kernel32.SetConsoleMode(stderr_handle, mode)

        except Exception:
            # 如果 Windows API 调用失败，依赖 colorama 进行处理
            pass

def setup_log_formatter(config: Optional[Any] = None):
    """设置日志格式化器，优化日志显示效果

    Args:
        config: 配置对象，包含log_display配置项
    """
    # 确保控制台编码设置正确
    _setup_console_encoding()

    # 在 Windows 系统上初始化控制台
    _init_windows_console()
    # 如果没有提供配置，使用默认配置
    if config is None or not hasattr(config, 'log_display'):
        log_display = {
            "max_message_length": 120,
            "show_module": True,
            "level_colors": {
                "DEBUG": "\033[36m",    # 青色
                "INFO": "\033[32m",     # 绿色
                "WARNING": "\033[33m",   # 黄色
                "ERROR": "\033[31m",     # 红色
                "CRITICAL": "\033[31;1m" # 亮红色
            },
            "time_format": "%H:%M:%S",
            "key_highlights": ["缓存", "API", "翻译", "错误", "检测", "网络", "配置"]
        }
    else:
        log_display = config.log_display

    # 检测是否支持颜色输出 - 改进的检测逻辑
    def _detect_color_support():
        """检测控制台是否支持颜色输出"""
        # 如果 colorama 可用，强制启用颜色支持
        if COLORAMA_AVAILABLE:
            return True

        # 检查环境变量
        if os.environ.get('FORCE_COLOR', '').lower() in ('1', 'true', 'yes'):
            return True
        if os.environ.get('NO_COLOR', '').lower() in ('1', 'true', 'yes'):
            return False

        # 检查是否在打包后的环境中运行
        if getattr(sys, 'frozen', False):
            # 在打包后的环境中，如果是 Windows 系统，默认启用颜色支持
            if sys.platform.startswith('win'):
                return True

        # 传统的 TTY 检测
        try:
            return hasattr(sys.stdout, 'isatty') and sys.stdout.isatty()
        except (AttributeError, OSError):
            return False

    color_supported = _detect_color_support()

    # 自定义格式化器
    class HighlightFormatter(logging.Formatter):
        def format(self, record):
            # 保存原始消息
            original_msg = record.msg

            # 处理消息长度
            if isinstance(record.msg, str) and len(record.msg) > log_display["max_message_length"]:
                record.msg = record.msg[:log_display["max_message_length"]] + "..."

            # 格式化消息
            message = super().format(record)

            # 恢复原始消息，避免影响日志文件
            record.msg = original_msg

            # 如果支持颜色，添加关键词高亮
            if color_supported:
                for word in log_display["key_highlights"]:
                    if isinstance(message, str) and word in message:
                        message = message.replace(
                            word,
                            f"\033[1;33m{word}\033[0m"  # 黄色高亮
                        )

            return message

    # 获取根日志记录器
    root_logger = logging.getLogger()

    # 清除已有控制台处理器，保留文件处理器
    for handler in root_logger.handlers[:]:
        if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
            root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()

    # 根据是否支持颜色决定格式
    if color_supported:
        # 带颜色的格式
        formatter = HighlightFormatter(
            f"{log_display['level_colors'].get('INFO', '')}"
            f"%(asctime)s\033[0m "
            f"%(levelcolor)s%(levelname)-8s\033[0m "
            f"{log_display['level_colors'].get('INFO', '')}"
            f"%(modinfo)s\033[0m "
            f"%(message)s"
        )
    else:
        # 不带颜色的格式
        formatter = HighlightFormatter(
            f"%(asctime)s "
            f"%(levelname)-8s "
            f"%(modinfo)s "
            f"%(message)s"
        )

    # 设置日期格式
    formatter.datefmt = log_display["time_format"]

    # 注入自定义属性
    old_factory = logging.getLogRecordFactory()
    def record_factory(*args, **kwargs):
        record = old_factory(*args, **kwargs)

        # 添加模块信息
        if log_display["show_module"]:
            module = record.module
            # 如果模块名过长，截断
            if len(module) > 12:
                module = module[:10] + ".."
            record.modinfo = f"[{module}]"
        else:
            record.modinfo = ""

        # 添加日志级别颜色
        if color_supported:
            record.levelcolor = log_display["level_colors"].get(record.levelname, "\033[0m")
        else:
            record.levelcolor = ""

        return record

    logging.setLogRecordFactory(record_factory)

    # 应用格式化器
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 保持原有日志级别
    if root_logger.level == 0:  # 未设置级别
        root_logger.setLevel(logging.INFO)

if __name__ == "__main__":
    # 简单测试
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger()

    # 应用格式化器
    setup_log_formatter()

    # 测试不同级别的日志
    logger.debug("这是一条调试日志")
    logger.info("这是一条信息日志，包含关键词：缓存")
    logger.warning("这是一条警告日志，包含关键词：网络")
    logger.error("这是一条错误日志，包含关键词：API")
    logger.critical("这是一条严重错误日志，包含关键词：翻译")

    # 测试长消息
    logger.info("这是一条非常长的日志消息" + "."*200 + "结束")