#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制台显示测试工具
用于测试中文字符显示和颜色输出在打包环境中的兼容性
"""

import sys
import os
import platform
import logging

# 添加当前目录到路径，确保能导入log_formatter
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from log_formatter import setup_log_formatter, _setup_console_encoding, _detect_color_support, COLORAMA_AVAILABLE
except ImportError as e:
    print(f"导入log_formatter失败: {e}")
    sys.exit(1)

def test_basic_console_output():
    """测试基本控制台输出"""
    print("=" * 60)
    print("基本控制台输出测试")
    print("=" * 60)
    
    # 测试中文字符
    print("中文字符测试：你好世界！这是一个测试。")
    print("特殊字符测试：©®™€£¥§¶†‡•…‰‹›""''–—")
    print("数字和英文：Hello World 123456789")
    
    # 测试长文本
    long_text = "这是一段很长的中文文本，" * 10
    print(f"长文本测试：{long_text}")
    
    print("基本控制台输出测试完成\n")

def test_color_output():
    """测试颜色输出"""
    print("=" * 60)
    print("颜色输出测试")
    print("=" * 60)
    
    # 检测颜色支持
    color_support = _detect_color_support()
    print(f"颜色支持检测结果: {color_support}")
    print(f"Colorama可用性: {COLORAMA_AVAILABLE}")
    print(f"系统平台: {platform.system()}")
    print(f"是否打包环境: {getattr(sys, 'frozen', False)}")
    print(f"Python版本: {sys.version}")
    print(f"控制台编码: {sys.stdout.encoding}")
    
    if COLORAMA_AVAILABLE:
        try:
            import colorama
            from colorama import Fore, Back, Style
            colorama.init(autoreset=True)
            
            print("\n使用Colorama的颜色测试：")
            print(f"{Fore.RED}红色文字{Style.RESET_ALL}")
            print(f"{Fore.GREEN}绿色文字{Style.RESET_ALL}")
            print(f"{Fore.BLUE}蓝色文字{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}黄色文字{Style.RESET_ALL}")
            print(f"{Fore.MAGENTA}紫色文字{Style.RESET_ALL}")
            print(f"{Fore.CYAN}青色文字{Style.RESET_ALL}")
            print(f"{Style.BRIGHT}{Fore.WHITE}亮白色文字{Style.RESET_ALL}")
            
            # 测试中文颜色
            print(f"{Fore.RED}红色中文：你好世界！{Style.RESET_ALL}")
            print(f"{Fore.GREEN}绿色中文：翻译测试{Style.RESET_ALL}")
            print(f"{Fore.BLUE}蓝色中文：API调用{Style.RESET_ALL}")
            
        except Exception as e:
            print(f"Colorama颜色测试失败: {e}")
    
    # 测试ANSI颜色代码
    print("\n使用ANSI颜色代码测试：")
    print("\033[31m红色ANSI文字\033[0m")
    print("\033[32m绿色ANSI文字\033[0m")
    print("\033[34m蓝色ANSI文字\033[0m")
    print("\033[33m黄色ANSI文字\033[0m")
    print("\033[35m紫色ANSI文字\033[0m")
    print("\033[36m青色ANSI文字\033[0m")
    
    # 测试ANSI中文颜色
    print("\033[31m红色ANSI中文：你好世界！\033[0m")
    print("\033[32m绿色ANSI中文：翻译测试\033[0m")
    print("\033[34m蓝色ANSI中文：API调用\033[0m")
    
    print("颜色输出测试完成\n")

def test_log_formatter():
    """测试日志格式化器"""
    print("=" * 60)
    print("日志格式化器测试")
    print("=" * 60)
    
    # 设置控制台编码
    _setup_console_encoding()
    
    # 清除现有的日志处理器
    logging.getLogger().handlers.clear()
    
    # 设置基本日志配置
    logging.basicConfig(level=logging.DEBUG, handlers=[])
    logger = logging.getLogger()
    
    # 应用日志格式化器
    setup_log_formatter()
    
    print("开始日志输出测试：\n")
    
    # 测试不同级别的日志
    logger.debug("这是一条调试日志 - 测试中文显示")
    logger.info("这是一条信息日志，包含关键词：缓存")
    logger.warning("这是一条警告日志，包含关键词：网络")
    logger.error("这是一条错误日志，包含关键词：API")
    logger.critical("这是一条严重错误日志，包含关键词：翻译")
    
    # 测试中文日志
    logger.info("中文日志测试：翻译功能正常工作")
    logger.warning("中文警告：网络连接可能不稳定")
    logger.error("中文错误：API调用失败")
    
    # 测试长消息
    long_message = "这是一条非常长的日志消息，用于测试消息截断功能。" * 5
    logger.info(long_message)
    
    # 测试特殊字符
    logger.info("特殊字符测试：©®™€£¥§¶†‡•…‰‹›""''–—")
    
    # 测试多行消息
    multiline_message = """这是一条多行日志消息：
第一行：基本信息
第二行：详细信息
第三行：结束信息"""
    logger.info(multiline_message)
    
    print("\n日志格式化器测试完成\n")

def main():
    """主测试函数"""
    print("控制台显示兼容性测试工具")
    print(f"运行环境：{platform.system()} {platform.release()}")
    print(f"Python版本：{sys.version}")
    print(f"是否打包环境：{getattr(sys, 'frozen', False)}")
    print()
    
    try:
        # 测试基本控制台输出
        test_basic_console_output()
        
        # 测试颜色输出
        test_color_output()
        
        # 测试日志格式化器
        test_log_formatter()
        
        print("=" * 60)
        print("所有测试完成！")
        print("如果您看到了正确的中文字符和颜色，说明修复成功。")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误：{e}")
        import traceback
        traceback.print_exc()
    
    # 等待用户输入以便查看结果
    try:
        input("\n按回车键退出...")
    except (EOFError, KeyboardInterrupt):
        pass

if __name__ == "__main__":
    main()
