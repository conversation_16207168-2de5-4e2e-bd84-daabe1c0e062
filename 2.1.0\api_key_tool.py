#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API密钥加密解密工具

一个简单的API密钥加密解密工具，使用AES-GCM加密算法。
通过菜单选择功能，可以轻松加密或解密API密钥。
"""

import os
import time
import sys
from api_crypto import ApiCrypto, encrypt_api_key, decrypt_api_key

def clear_screen():
    """清空控制台屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_color(text, color=None):
    """带颜色打印文本
    
    颜色选项: green, red, yellow, blue, purple, cyan
    """
    colors = {
        'green': '\033[92m',
        'red': '\033[91m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'end': '\033[0m'
    }
    
    if color and color in colors:
        print(f"{colors[color]}{text}{colors['end']}")
    else:
        print(text)

def show_menu():
    """显示主菜单"""
    clear_screen()
    print("=" * 60)
    print_color("         API密钥加密解密工具 v1.0", "cyan")
    print("=" * 60)
    print("\n请选择操作:")
    print_color("  1. 加密API密钥", "green")
    print_color("  2. 解密API密钥", "blue")
    print_color("  3. 退出程序", "yellow")
    print("\n" + "=" * 60)
    return input("\n请输入选项 (1-3): ").strip()

def encrypt_api_key_menu():
    """加密API密钥菜单"""
    clear_screen()
    print("=" * 60)
    print_color("               加密API密钥", "green")
    print("=" * 60)
    
    api_key = input("\n请输入要加密的API密钥: ").strip()
    if not api_key:
        print_color("\nAPI密钥不能为空!", "red")
        input("按回车返回主菜单...")
        return
    
    use_custom_password = input("\n是否使用自定义密码? (y/n, 默认n): ").strip().lower()
    password = None
    
    if use_custom_password == 'y':
        password = input("请输入自定义密码: ").strip()
        if not password:
            print_color("未输入密码，将使用默认密码", "yellow")
            password = None
    
    print_color("\n正在加密...", "cyan")
    sys.stdout.flush()  # 确保立即显示
    time.sleep(0.5)  # 短暂延迟，增强体验
    
    result = encrypt_api_key(api_key, password)
    
    print("\n加密结果:")
    print("-" * 60)
    print_color(result, "purple")
    print("-" * 60)
    print("\n可以将此加密结果复制到config.yaml文件中的api_key字段")
    print("加密后的API密钥会在程序运行时自动解密")
    
    input("\n按回车返回主菜单...")

def decrypt_api_key_menu():
    """解密API密钥菜单"""
    clear_screen()
    print("=" * 60)
    print_color("               解密API密钥", "blue")
    print("=" * 60)
    
    encrypted_api_key = input("\n请输入要解密的API密钥: ").strip()
    if not encrypted_api_key:
        print_color("\n加密的API密钥不能为空!", "red")
        input("按回车返回主菜单...")
        return
    
    use_custom_password = input("\n是否使用自定义密码? (y/n, 默认n): ").strip().lower()
    password = None
    
    if use_custom_password == 'y':
        password = input("请输入自定义密码: ").strip()
        if not password:
            print_color("未输入密码，将使用默认密码", "yellow")
            password = None
    
    print_color("\n正在解密...", "cyan")
    sys.stdout.flush()  # 确保立即显示
    time.sleep(0.5)  # 短暂延迟，增强体验
    
    result = decrypt_api_key(encrypted_api_key, password)
    
    print("\n解密结果:")
    print("-" * 60)
    if result:
        print_color(result, "green")
    else:
        print_color("解密失败!", "red")
    print("-" * 60)
    
    if not result:
        print_color("\n解密失败可能的原因:", "yellow")
        print("1. 输入的加密API密钥不正确")
        print("2. 解密密码不正确")
        print("3. 输入的不是有效的加密API密钥")
    
    input("\n按回车返回主菜单...")

def main():
    """主函数"""
    try:
        while True:
            choice = show_menu()
            
            if choice == '1':
                encrypt_api_key_menu()
            elif choice == '2':
                decrypt_api_key_menu()
            elif choice == '3':
                clear_screen()
                print_color("感谢使用，再见!", "green")
                break
            else:
                print_color("\n无效选项，请重新输入!", "red")
                input("按回车继续...")
    except KeyboardInterrupt:
        # 处理Ctrl+C
        clear_screen()
        print_color("\n程序已被用户中断，再见!", "yellow")
    except Exception as e:
        # 处理其他异常
        clear_screen()
        print_color(f"\n发生错误: {e}", "red")
        print("请确保已正确安装所需库: pip install cryptography")
        input("\n按回车退出...")

if __name__ == "__main__":
    main() 