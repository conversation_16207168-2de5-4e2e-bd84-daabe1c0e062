('C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\2.0.8\\build\\多语言互译器2.0.8\\PYZ-00.pyz',
 [('PIL',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyrepl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('aiohappyeyeballs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohttp',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.log',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('annotated_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('api_crypto',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\2.0.8\\api_crypto.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\bz2.py',
   'PYMODULE'),
  ('cachetools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cachetools\\__init__.py',
   'PYMODULE'),
  ('cachetools._decorators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cachetools\\_decorators.py',
   'PYMODULE'),
  ('cachetools.keys',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cachetools\\keys.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('config_management',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\2.0.8\\config_management.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\copy.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.pbkdf2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\pbkdf2.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\fractions.py',
   'PYMODULE'),
  ('frozenlist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\glob.py',
   'PYMODULE'),
  ('google', '-', 'PYMODULE'),
  ('google._upb', '-', 'PYMODULE'),
  ('google.ai', '-', 'PYMODULE'),
  ('google.ai.generativelanguage',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage.gapic_version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage\\gapic_version.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.gapic_version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\gapic_version.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.pagers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.cache_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\cache_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.discuss_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.pagers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.file_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.generative_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\generative_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.pagers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.model_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.pagers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.permission_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.prediction_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\prediction_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.pagers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\pagers.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.retriever_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\retriever_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\async_client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\client.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\grpc.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.grpc_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\grpc_asyncio.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\rest.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\rest_base.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\__init__.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.cache_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\cache_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.cached_content',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\cached_content.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.citation',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\citation.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.content',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\content.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.discuss_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\discuss_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.file',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\file.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.file_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\file_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.generative_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\generative_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.model',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\model.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.model_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\model_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.permission',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\permission.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.permission_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\permission_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.prediction_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\prediction_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.retriever',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\retriever.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.retriever_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\retriever_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.safety',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\safety.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.text_service',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\text_service.py',
   'PYMODULE'),
  ('google.ai.generativelanguage_v1beta.types.tuned_model',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\types\\tuned_model.py',
   'PYMODULE'),
  ('google.api', '-', 'PYMODULE'),
  ('google.api.annotations_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api\\annotations_pb2.py',
   'PYMODULE'),
  ('google.api.client_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api\\client_pb2.py',
   'PYMODULE'),
  ('google.api.http_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api\\http_pb2.py',
   'PYMODULE'),
  ('google.api.launch_stage_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api\\launch_stage_pb2.py',
   'PYMODULE'),
  ('google.api_core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\__init__.py',
   'PYMODULE'),
  ('google.api_core._rest_streaming_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\_rest_streaming_base.py',
   'PYMODULE'),
  ('google.api_core.client_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\client_info.py',
   'PYMODULE'),
  ('google.api_core.client_logging',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\client_logging.py',
   'PYMODULE'),
  ('google.api_core.client_options',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\client_options.py',
   'PYMODULE'),
  ('google.api_core.datetime_helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\datetime_helpers.py',
   'PYMODULE'),
  ('google.api_core.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\exceptions.py',
   'PYMODULE'),
  ('google.api_core.future',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\future\\__init__.py',
   'PYMODULE'),
  ('google.api_core.future._helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\future\\_helpers.py',
   'PYMODULE'),
  ('google.api_core.future.async_future',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\future\\async_future.py',
   'PYMODULE'),
  ('google.api_core.future.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\future\\base.py',
   'PYMODULE'),
  ('google.api_core.future.polling',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\future\\polling.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\__init__.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.client_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\client_info.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\config.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.config_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\config_async.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.method',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\method.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.method_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\method_async.py',
   'PYMODULE'),
  ('google.api_core.gapic_v1.routing_header',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\routing_header.py',
   'PYMODULE'),
  ('google.api_core.grpc_helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\grpc_helpers.py',
   'PYMODULE'),
  ('google.api_core.grpc_helpers_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\grpc_helpers_async.py',
   'PYMODULE'),
  ('google.api_core.operation',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operation.py',
   'PYMODULE'),
  ('google.api_core.operation_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operation_async.py',
   'PYMODULE'),
  ('google.api_core.operations_v1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\__init__.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.abstract_operations_base_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\abstract_operations_base_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.abstract_operations_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\abstract_operations_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.operations_async_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_async_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.operations_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_client.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.operations_rest_client_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_rest_client_async.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.pagers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\pagers.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.pagers_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\pagers_async.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.pagers_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\pagers_base.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\__init__.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\base.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports.rest',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\rest.py',
   'PYMODULE'),
  ('google.api_core.operations_v1.transports.rest_asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\rest_asyncio.py',
   'PYMODULE'),
  ('google.api_core.page_iterator',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\page_iterator.py',
   'PYMODULE'),
  ('google.api_core.page_iterator_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\page_iterator_async.py',
   'PYMODULE'),
  ('google.api_core.path_template',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\path_template.py',
   'PYMODULE'),
  ('google.api_core.protobuf_helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\protobuf_helpers.py',
   'PYMODULE'),
  ('google.api_core.rest_helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\rest_helpers.py',
   'PYMODULE'),
  ('google.api_core.rest_streaming',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\rest_streaming.py',
   'PYMODULE'),
  ('google.api_core.retry',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry\\__init__.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry\\retry_base.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_streaming',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry\\retry_streaming.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_streaming_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry\\retry_streaming_async.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_unary',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry\\retry_unary.py',
   'PYMODULE'),
  ('google.api_core.retry.retry_unary_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry\\retry_unary_async.py',
   'PYMODULE'),
  ('google.api_core.retry_async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\retry_async.py',
   'PYMODULE'),
  ('google.api_core.timeout',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\timeout.py',
   'PYMODULE'),
  ('google.api_core.universe',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\universe.py',
   'PYMODULE'),
  ('google.api_core.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\version.py',
   'PYMODULE'),
  ('google.api_core.version_header',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\api_core\\version_header.py',
   'PYMODULE'),
  ('google.auth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\__init__.py',
   'PYMODULE'),
  ('google.auth._cloud_sdk',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_cloud_sdk.py',
   'PYMODULE'),
  ('google.auth._credentials_base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_credentials_base.py',
   'PYMODULE'),
  ('google.auth._default',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_default.py',
   'PYMODULE'),
  ('google.auth._exponential_backoff',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_exponential_backoff.py',
   'PYMODULE'),
  ('google.auth._helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_helpers.py',
   'PYMODULE'),
  ('google.auth._refresh_worker',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_refresh_worker.py',
   'PYMODULE'),
  ('google.auth._service_account_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\_service_account_info.py',
   'PYMODULE'),
  ('google.auth.aio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aio\\__init__.py',
   'PYMODULE'),
  ('google.auth.aio._helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aio\\_helpers.py',
   'PYMODULE'),
  ('google.auth.aio.credentials',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aio\\credentials.py',
   'PYMODULE'),
  ('google.auth.aio.transport',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aio\\transport\\__init__.py',
   'PYMODULE'),
  ('google.auth.aio.transport.aiohttp',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aio\\transport\\aiohttp.py',
   'PYMODULE'),
  ('google.auth.aio.transport.sessions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aio\\transport\\sessions.py',
   'PYMODULE'),
  ('google.auth.api_key',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\api_key.py',
   'PYMODULE'),
  ('google.auth.app_engine',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\app_engine.py',
   'PYMODULE'),
  ('google.auth.aws',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\aws.py',
   'PYMODULE'),
  ('google.auth.compute_engine',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\compute_engine\\__init__.py',
   'PYMODULE'),
  ('google.auth.compute_engine._metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\compute_engine\\_metadata.py',
   'PYMODULE'),
  ('google.auth.compute_engine.credentials',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\compute_engine\\credentials.py',
   'PYMODULE'),
  ('google.auth.credentials',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\credentials.py',
   'PYMODULE'),
  ('google.auth.crypt',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\crypt\\__init__.py',
   'PYMODULE'),
  ('google.auth.crypt._cryptography_rsa',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\crypt\\_cryptography_rsa.py',
   'PYMODULE'),
  ('google.auth.crypt._python_rsa',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\crypt\\_python_rsa.py',
   'PYMODULE'),
  ('google.auth.crypt.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\crypt\\base.py',
   'PYMODULE'),
  ('google.auth.crypt.es256',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\crypt\\es256.py',
   'PYMODULE'),
  ('google.auth.crypt.rsa',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\crypt\\rsa.py',
   'PYMODULE'),
  ('google.auth.environment_vars',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\environment_vars.py',
   'PYMODULE'),
  ('google.auth.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\exceptions.py',
   'PYMODULE'),
  ('google.auth.external_account',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\external_account.py',
   'PYMODULE'),
  ('google.auth.external_account_authorized_user',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\external_account_authorized_user.py',
   'PYMODULE'),
  ('google.auth.iam',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\iam.py',
   'PYMODULE'),
  ('google.auth.identity_pool',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\identity_pool.py',
   'PYMODULE'),
  ('google.auth.impersonated_credentials',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\impersonated_credentials.py',
   'PYMODULE'),
  ('google.auth.jwt',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\jwt.py',
   'PYMODULE'),
  ('google.auth.metrics',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\metrics.py',
   'PYMODULE'),
  ('google.auth.pluggable',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\pluggable.py',
   'PYMODULE'),
  ('google.auth.transport',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\__init__.py',
   'PYMODULE'),
  ('google.auth.transport._custom_tls_signer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\_custom_tls_signer.py',
   'PYMODULE'),
  ('google.auth.transport._http_client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\_http_client.py',
   'PYMODULE'),
  ('google.auth.transport._mtls_helper',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\_mtls_helper.py',
   'PYMODULE'),
  ('google.auth.transport.grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\grpc.py',
   'PYMODULE'),
  ('google.auth.transport.mtls',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\mtls.py',
   'PYMODULE'),
  ('google.auth.transport.requests',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\transport\\requests.py',
   'PYMODULE'),
  ('google.auth.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\auth\\version.py',
   'PYMODULE'),
  ('google.generativeai',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\__init__.py',
   'PYMODULE'),
  ('google.generativeai.caching',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\caching.py',
   'PYMODULE'),
  ('google.generativeai.client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\client.py',
   'PYMODULE'),
  ('google.generativeai.embedding',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\embedding.py',
   'PYMODULE'),
  ('google.generativeai.files',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\files.py',
   'PYMODULE'),
  ('google.generativeai.generative_models',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\generative_models.py',
   'PYMODULE'),
  ('google.generativeai.models',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\models.py',
   'PYMODULE'),
  ('google.generativeai.operations',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\operations.py',
   'PYMODULE'),
  ('google.generativeai.protos',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\protos.py',
   'PYMODULE'),
  ('google.generativeai.responder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\responder.py',
   'PYMODULE'),
  ('google.generativeai.string_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\string_utils.py',
   'PYMODULE'),
  ('google.generativeai.types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\__init__.py',
   'PYMODULE'),
  ('google.generativeai.types.caching_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\caching_types.py',
   'PYMODULE'),
  ('google.generativeai.types.citation_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\citation_types.py',
   'PYMODULE'),
  ('google.generativeai.types.content_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\content_types.py',
   'PYMODULE'),
  ('google.generativeai.types.file_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\file_types.py',
   'PYMODULE'),
  ('google.generativeai.types.generation_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\generation_types.py',
   'PYMODULE'),
  ('google.generativeai.types.helper_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\helper_types.py',
   'PYMODULE'),
  ('google.generativeai.types.model_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\model_types.py',
   'PYMODULE'),
  ('google.generativeai.types.permission_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\permission_types.py',
   'PYMODULE'),
  ('google.generativeai.types.safety_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\safety_types.py',
   'PYMODULE'),
  ('google.generativeai.types.text_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\types\\text_types.py',
   'PYMODULE'),
  ('google.generativeai.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\utils.py',
   'PYMODULE'),
  ('google.generativeai.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\generativeai\\version.py',
   'PYMODULE'),
  ('google.longrunning', '-', 'PYMODULE'),
  ('google.longrunning.operations_grpc_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\longrunning\\operations_grpc_pb2.py',
   'PYMODULE'),
  ('google.longrunning.operations_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\longrunning\\operations_pb2.py',
   'PYMODULE'),
  ('google.longrunning.operations_pb2_grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\longrunning\\operations_pb2_grpc.py',
   'PYMODULE'),
  ('google.longrunning.operations_proto_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\longrunning\\operations_proto_pb2.py',
   'PYMODULE'),
  ('google.oauth2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\__init__.py',
   'PYMODULE'),
  ('google.oauth2._client',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\_client.py',
   'PYMODULE'),
  ('google.oauth2.challenges',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\challenges.py',
   'PYMODULE'),
  ('google.oauth2.credentials',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\credentials.py',
   'PYMODULE'),
  ('google.oauth2.gdch_credentials',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\gdch_credentials.py',
   'PYMODULE'),
  ('google.oauth2.reauth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\reauth.py',
   'PYMODULE'),
  ('google.oauth2.service_account',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\service_account.py',
   'PYMODULE'),
  ('google.oauth2.sts',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\sts.py',
   'PYMODULE'),
  ('google.oauth2.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\utils.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_handler',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\webauthn_handler.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_handler_factory',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\webauthn_handler_factory.py',
   'PYMODULE'),
  ('google.oauth2.webauthn_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\oauth2\\webauthn_types.py',
   'PYMODULE'),
  ('google.protobuf',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.any_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\any_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\descriptor.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_database',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\descriptor_database.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\descriptor_pb2.py',
   'PYMODULE'),
  ('google.protobuf.descriptor_pool',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\descriptor_pool.py',
   'PYMODULE'),
  ('google.protobuf.duration_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\duration_pb2.py',
   'PYMODULE'),
  ('google.protobuf.empty_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\empty_pb2.py',
   'PYMODULE'),
  ('google.protobuf.field_mask_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\field_mask_pb2.py',
   'PYMODULE'),
  ('google.protobuf.internal',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.internal.api_implementation',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\api_implementation.py',
   'PYMODULE'),
  ('google.protobuf.internal.builder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\builder.py',
   'PYMODULE'),
  ('google.protobuf.internal.containers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\containers.py',
   'PYMODULE'),
  ('google.protobuf.internal.decoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\decoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.encoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\encoder.py',
   'PYMODULE'),
  ('google.protobuf.internal.enum_type_wrapper',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\enum_type_wrapper.py',
   'PYMODULE'),
  ('google.protobuf.internal.extension_dict',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\extension_dict.py',
   'PYMODULE'),
  ('google.protobuf.internal.field_mask',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\field_mask.py',
   'PYMODULE'),
  ('google.protobuf.internal.message_listener',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\message_listener.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_edition_defaults',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\python_edition_defaults.py',
   'PYMODULE'),
  ('google.protobuf.internal.python_message',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\python_message.py',
   'PYMODULE'),
  ('google.protobuf.internal.type_checkers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\type_checkers.py',
   'PYMODULE'),
  ('google.protobuf.internal.well_known_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\well_known_types.py',
   'PYMODULE'),
  ('google.protobuf.internal.wire_format',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\internal\\wire_format.py',
   'PYMODULE'),
  ('google.protobuf.json_format',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\json_format.py',
   'PYMODULE'),
  ('google.protobuf.message',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\message.py',
   'PYMODULE'),
  ('google.protobuf.message_factory',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\message_factory.py',
   'PYMODULE'),
  ('google.protobuf.pyext',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\pyext\\__init__.py',
   'PYMODULE'),
  ('google.protobuf.pyext.cpp_message',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\pyext\\cpp_message.py',
   'PYMODULE'),
  ('google.protobuf.reflection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\reflection.py',
   'PYMODULE'),
  ('google.protobuf.runtime_version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\runtime_version.py',
   'PYMODULE'),
  ('google.protobuf.service_reflection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\service_reflection.py',
   'PYMODULE'),
  ('google.protobuf.struct_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\struct_pb2.py',
   'PYMODULE'),
  ('google.protobuf.symbol_database',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\symbol_database.py',
   'PYMODULE'),
  ('google.protobuf.text_encoding',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\text_encoding.py',
   'PYMODULE'),
  ('google.protobuf.text_format',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\text_format.py',
   'PYMODULE'),
  ('google.protobuf.timestamp_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\timestamp_pb2.py',
   'PYMODULE'),
  ('google.protobuf.unknown_fields',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\unknown_fields.py',
   'PYMODULE'),
  ('google.protobuf.wrappers_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\protobuf\\wrappers_pb2.py',
   'PYMODULE'),
  ('google.rpc', '-', 'PYMODULE'),
  ('google.rpc.code_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\rpc\\code_pb2.py',
   'PYMODULE'),
  ('google.rpc.error_details_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\rpc\\error_details_pb2.py',
   'PYMODULE'),
  ('google.rpc.status_pb2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google\\rpc\\status_pb2.py',
   'PYMODULE'),
  ('google_auth_httplib2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\google_auth_httplib2.py',
   'PYMODULE'),
  ('googleapiclient',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\__init__.py',
   'PYMODULE'),
  ('googleapiclient._auth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\_auth.py',
   'PYMODULE'),
  ('googleapiclient._helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\_helpers.py',
   'PYMODULE'),
  ('googleapiclient.discovery',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\discovery.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\__init__.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache.appengine_memcache',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\appengine_memcache.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\base.py',
   'PYMODULE'),
  ('googleapiclient.discovery_cache.file_cache',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\discovery_cache\\file_cache.py',
   'PYMODULE'),
  ('googleapiclient.errors',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\errors.py',
   'PYMODULE'),
  ('googleapiclient.http',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\http.py',
   'PYMODULE'),
  ('googleapiclient.mimeparse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\mimeparse.py',
   'PYMODULE'),
  ('googleapiclient.model',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\model.py',
   'PYMODULE'),
  ('googleapiclient.schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\schema.py',
   'PYMODULE'),
  ('googleapiclient.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\googleapiclient\\version.py',
   'PYMODULE'),
  ('grpc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\__init__.py',
   'PYMODULE'),
  ('grpc._auth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_auth.py',
   'PYMODULE'),
  ('grpc._channel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_channel.py',
   'PYMODULE'),
  ('grpc._common',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_common.py',
   'PYMODULE'),
  ('grpc._compression',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_compression.py',
   'PYMODULE'),
  ('grpc._cython',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_cython\\__init__.py',
   'PYMODULE'),
  ('grpc._grpcio_metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_grpcio_metadata.py',
   'PYMODULE'),
  ('grpc._interceptor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_interceptor.py',
   'PYMODULE'),
  ('grpc._observability',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_observability.py',
   'PYMODULE'),
  ('grpc._plugin_wrapping',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_plugin_wrapping.py',
   'PYMODULE'),
  ('grpc._runtime_protos',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_runtime_protos.py',
   'PYMODULE'),
  ('grpc._server',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_server.py',
   'PYMODULE'),
  ('grpc._simple_stubs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_simple_stubs.py',
   'PYMODULE'),
  ('grpc._typing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_typing.py',
   'PYMODULE'),
  ('grpc._utilities',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\_utilities.py',
   'PYMODULE'),
  ('grpc.aio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\__init__.py',
   'PYMODULE'),
  ('grpc.aio._base_call',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_base_call.py',
   'PYMODULE'),
  ('grpc.aio._base_channel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_base_channel.py',
   'PYMODULE'),
  ('grpc.aio._base_server',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_base_server.py',
   'PYMODULE'),
  ('grpc.aio._call',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_call.py',
   'PYMODULE'),
  ('grpc.aio._channel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_channel.py',
   'PYMODULE'),
  ('grpc.aio._interceptor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_interceptor.py',
   'PYMODULE'),
  ('grpc.aio._metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_metadata.py',
   'PYMODULE'),
  ('grpc.aio._server',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_server.py',
   'PYMODULE'),
  ('grpc.aio._typing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_typing.py',
   'PYMODULE'),
  ('grpc.aio._utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\aio\\_utils.py',
   'PYMODULE'),
  ('grpc.experimental',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\experimental\\__init__.py',
   'PYMODULE'),
  ('grpc.experimental.aio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc\\experimental\\aio\\__init__.py',
   'PYMODULE'),
  ('grpc_status',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc_status\\__init__.py',
   'PYMODULE'),
  ('grpc_status._async',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc_status\\_async.py',
   'PYMODULE'),
  ('grpc_status._common',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc_status\\_common.py',
   'PYMODULE'),
  ('grpc_status.rpc_status',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\grpc_status\\rpc_status.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('httplib2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\httplib2\\__init__.py',
   'PYMODULE'),
  ('httplib2.auth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\httplib2\\auth.py',
   'PYMODULE'),
  ('httplib2.certs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\httplib2\\certs.py',
   'PYMODULE'),
  ('httplib2.error',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\httplib2\\error.py',
   'PYMODULE'),
  ('httplib2.iri2uri',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\httplib2\\iri2uri.py',
   'PYMODULE'),
  ('httplib2.socks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\httplib2\\socks.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('mouseinfo',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('multidict',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\opcode.py',
   'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pprint.py',
   'PYMODULE'),
  ('propcache',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('propcache.api',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('proto',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\__init__.py',
   'PYMODULE'),
  ('proto._file_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\_file_info.py',
   'PYMODULE'),
  ('proto._package_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\_package_info.py',
   'PYMODULE'),
  ('proto.datetime_helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\datetime_helpers.py',
   'PYMODULE'),
  ('proto.enums',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\enums.py',
   'PYMODULE'),
  ('proto.fields',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\fields.py',
   'PYMODULE'),
  ('proto.marshal',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\__init__.py',
   'PYMODULE'),
  ('proto.marshal.collections',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\collections\\__init__.py',
   'PYMODULE'),
  ('proto.marshal.collections.maps',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\collections\\maps.py',
   'PYMODULE'),
  ('proto.marshal.collections.repeated',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\collections\\repeated.py',
   'PYMODULE'),
  ('proto.marshal.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\compat.py',
   'PYMODULE'),
  ('proto.marshal.marshal',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\marshal.py',
   'PYMODULE'),
  ('proto.marshal.rules',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\__init__.py',
   'PYMODULE'),
  ('proto.marshal.rules.bytes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\bytes.py',
   'PYMODULE'),
  ('proto.marshal.rules.dates',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\dates.py',
   'PYMODULE'),
  ('proto.marshal.rules.enums',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\enums.py',
   'PYMODULE'),
  ('proto.marshal.rules.field_mask',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\field_mask.py',
   'PYMODULE'),
  ('proto.marshal.rules.message',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\message.py',
   'PYMODULE'),
  ('proto.marshal.rules.stringy_numbers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\stringy_numbers.py',
   'PYMODULE'),
  ('proto.marshal.rules.struct',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\struct.py',
   'PYMODULE'),
  ('proto.marshal.rules.wrappers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\marshal\\rules\\wrappers.py',
   'PYMODULE'),
  ('proto.message',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\message.py',
   'PYMODULE'),
  ('proto.modules',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\modules.py',
   'PYMODULE'),
  ('proto.primitives',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\primitives.py',
   'PYMODULE'),
  ('proto.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\utils.py',
   'PYMODULE'),
  ('proto.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\proto\\version.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pyasn1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\ber\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.decoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\ber\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.encoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\ber\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.ber.eoo',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\ber\\eoo.py',
   'PYMODULE'),
  ('pyasn1.codec.cer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\cer\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.decoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\cer\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.cer.encoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\cer\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\der\\__init__.py',
   'PYMODULE'),
  ('pyasn1.codec.der.decoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\der\\decoder.py',
   'PYMODULE'),
  ('pyasn1.codec.der.encoder',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\der\\encoder.py',
   'PYMODULE'),
  ('pyasn1.codec.streaming',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\codec\\streaming.py',
   'PYMODULE'),
  ('pyasn1.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\compat\\__init__.py',
   'PYMODULE'),
  ('pyasn1.compat.integer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\compat\\integer.py',
   'PYMODULE'),
  ('pyasn1.debug',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\debug.py',
   'PYMODULE'),
  ('pyasn1.error',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\error.py',
   'PYMODULE'),
  ('pyasn1.type',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\__init__.py',
   'PYMODULE'),
  ('pyasn1.type.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\base.py',
   'PYMODULE'),
  ('pyasn1.type.char',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\char.py',
   'PYMODULE'),
  ('pyasn1.type.constraint',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\constraint.py',
   'PYMODULE'),
  ('pyasn1.type.error',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\error.py',
   'PYMODULE'),
  ('pyasn1.type.namedtype',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\namedtype.py',
   'PYMODULE'),
  ('pyasn1.type.namedval',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\namedval.py',
   'PYMODULE'),
  ('pyasn1.type.opentype',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\opentype.py',
   'PYMODULE'),
  ('pyasn1.type.tag',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\tag.py',
   'PYMODULE'),
  ('pyasn1.type.tagmap',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\tagmap.py',
   'PYMODULE'),
  ('pyasn1.type.univ',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\univ.py',
   'PYMODULE'),
  ('pyasn1.type.useful',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1\\type\\useful.py',
   'PYMODULE'),
  ('pyasn1_modules',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1_modules\\__init__.py',
   'PYMODULE'),
  ('pyasn1_modules.pem',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1_modules\\pem.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2251',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1_modules\\rfc2251.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc2459',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1_modules\\rfc2459.py',
   'PYMODULE'),
  ('pyasn1_modules.rfc5208',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyasn1_modules\\rfc5208.py',
   'PYMODULE'),
  ('pyautogui',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pycld2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycld2\\__init__.py',
   'PYMODULE'),
  ('pycparser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydantic',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._docs_extraction',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_docs_extraction.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._git',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_git.py',
   'PYMODULE'),
  ('pydantic._internal._import_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_import_utils.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._namespace_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_namespace_utils.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_gather',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_schema_gather.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._serializers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_serializers.py',
   'PYMODULE'),
  ('pydantic._internal._signature',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_signature.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.aliases',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\aliases.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.experimental',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\experimental\\__init__.py',
   'PYMODULE'),
  ('pydantic.experimental.arguments_schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\experimental\\arguments_schema.py',
   'PYMODULE'),
  ('pydantic.experimental.pipeline',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\experimental\\pipeline.py',
   'PYMODULE'),
  ('pydantic.fields',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygetwindow',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pymsgbox',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pynput',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\__init__.py',
   'PYMODULE'),
  ('pynput._info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_info.py',
   'PYMODULE'),
  ('pynput._util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\__init__.py',
   'PYMODULE'),
  ('pynput._util.darwin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\darwin.py',
   'PYMODULE'),
  ('pynput._util.darwin_vks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\darwin_vks.py',
   'PYMODULE'),
  ('pynput._util.uinput',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\uinput.py',
   'PYMODULE'),
  ('pynput._util.win32',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\win32.py',
   'PYMODULE'),
  ('pynput._util.win32_vks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\win32_vks.py',
   'PYMODULE'),
  ('pynput._util.xorg',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\xorg.py',
   'PYMODULE'),
  ('pynput._util.xorg_keysyms',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\_util\\xorg_keysyms.py',
   'PYMODULE'),
  ('pynput.keyboard',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\__init__.py',
   'PYMODULE'),
  ('pynput.keyboard._base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\_base.py',
   'PYMODULE'),
  ('pynput.keyboard._darwin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\_darwin.py',
   'PYMODULE'),
  ('pynput.keyboard._dummy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\_dummy.py',
   'PYMODULE'),
  ('pynput.keyboard._uinput',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\_uinput.py',
   'PYMODULE'),
  ('pynput.keyboard._win32',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\_win32.py',
   'PYMODULE'),
  ('pynput.keyboard._xorg',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\keyboard\\_xorg.py',
   'PYMODULE'),
  ('pynput.mouse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\mouse\\__init__.py',
   'PYMODULE'),
  ('pynput.mouse._base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\mouse\\_base.py',
   'PYMODULE'),
  ('pynput.mouse._darwin',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\mouse\\_darwin.py',
   'PYMODULE'),
  ('pynput.mouse._dummy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\mouse\\_dummy.py',
   'PYMODULE'),
  ('pynput.mouse._win32',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\mouse\\_win32.py',
   'PYMODULE'),
  ('pynput.mouse._xorg',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pynput\\mouse\\_xorg.py',
   'PYMODULE'),
  ('pyparsing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.common',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('pyparsing.results',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('pyperclip',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyrect',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('pyscreeze',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pytweening',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\random.py',
   'PYMODULE'),
  ('regex',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\regex\\__init__.py',
   'PYMODULE'),
  ('regex._regex_core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\regex\\_regex_core.py',
   'PYMODULE'),
  ('regex.regex',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\regex\\regex.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('rsa',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\__init__.py',
   'PYMODULE'),
  ('rsa.asn1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\asn1.py',
   'PYMODULE'),
  ('rsa.common',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\common.py',
   'PYMODULE'),
  ('rsa.core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\core.py',
   'PYMODULE'),
  ('rsa.key',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\key.py',
   'PYMODULE'),
  ('rsa.parallel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\parallel.py',
   'PYMODULE'),
  ('rsa.pem',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\pem.py',
   'PYMODULE'),
  ('rsa.pkcs1',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\pkcs1.py',
   'PYMODULE'),
  ('rsa.prime',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\prime.py',
   'PYMODULE'),
  ('rsa.randnum',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\randnum.py',
   'PYMODULE'),
  ('rsa.transform',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\rsa\\transform.py',
   'PYMODULE'),
  ('ruamel', '-', 'PYMODULE'),
  ('ruamel.yaml',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\__init__.py',
   'PYMODULE'),
  ('ruamel.yaml.anchor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\anchor.py',
   'PYMODULE'),
  ('ruamel.yaml.comments',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\comments.py',
   'PYMODULE'),
  ('ruamel.yaml.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\compat.py',
   'PYMODULE'),
  ('ruamel.yaml.composer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\composer.py',
   'PYMODULE'),
  ('ruamel.yaml.constructor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\constructor.py',
   'PYMODULE'),
  ('ruamel.yaml.cyaml',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\cyaml.py',
   'PYMODULE'),
  ('ruamel.yaml.docinfo',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\docinfo.py',
   'PYMODULE'),
  ('ruamel.yaml.dumper',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\dumper.py',
   'PYMODULE'),
  ('ruamel.yaml.emitter',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\emitter.py',
   'PYMODULE'),
  ('ruamel.yaml.error',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\error.py',
   'PYMODULE'),
  ('ruamel.yaml.events',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\events.py',
   'PYMODULE'),
  ('ruamel.yaml.loader',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\loader.py',
   'PYMODULE'),
  ('ruamel.yaml.main',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\main.py',
   'PYMODULE'),
  ('ruamel.yaml.nodes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\nodes.py',
   'PYMODULE'),
  ('ruamel.yaml.parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\parser.py',
   'PYMODULE'),
  ('ruamel.yaml.reader',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\reader.py',
   'PYMODULE'),
  ('ruamel.yaml.representer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\representer.py',
   'PYMODULE'),
  ('ruamel.yaml.resolver',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\resolver.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarbool',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\scalarbool.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarfloat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\scalarfloat.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarint',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\scalarint.py',
   'PYMODULE'),
  ('ruamel.yaml.scalarstring',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\scalarstring.py',
   'PYMODULE'),
  ('ruamel.yaml.scanner',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\scanner.py',
   'PYMODULE'),
  ('ruamel.yaml.serializer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\serializer.py',
   'PYMODULE'),
  ('ruamel.yaml.tag',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\tag.py',
   'PYMODULE'),
  ('ruamel.yaml.timestamp',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\timestamp.py',
   'PYMODULE'),
  ('ruamel.yaml.tokens',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\tokens.py',
   'PYMODULE'),
  ('ruamel.yaml.util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\ruamel\\yaml\\util.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\signal.py',
   'PYMODULE'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site.py',
   'PYMODULE'),
  ('six',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\smtplib.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tqdm',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.asyncio',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\asyncio.py',
   'PYMODULE'),
  ('tqdm.auto',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\auto.py',
   'PYMODULE'),
  ('tqdm.autonotebook',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\autonotebook.py',
   'PYMODULE'),
  ('tqdm.cli',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('typing_inspection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\typing_inspection\\__init__.py',
   'PYMODULE'),
  ('typing_inspection.introspection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\typing_inspection\\introspection.py',
   'PYMODULE'),
  ('typing_inspection.typing_objects',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\typing_inspection\\typing_objects.py',
   'PYMODULE'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('uritemplate',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\uritemplate\\__init__.py',
   'PYMODULE'),
  ('uritemplate.api',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\uritemplate\\api.py',
   'PYMODULE'),
  ('uritemplate.orderedset',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\uritemplate\\orderedset.py',
   'PYMODULE'),
  ('uritemplate.template',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\uritemplate\\template.py',
   'PYMODULE'),
  ('uritemplate.variable',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\uritemplate\\variable.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yarl',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._parse',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('yarl._path',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._query',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('yarl._quoters',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url',
   'C:\\Users\\<USER>\\Desktop\\代码\\翻译软件代码\\多语言互译\\venv\\Lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('zoneinfo._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_common.py',
   'PYMODULE'),
  ('zoneinfo._tzpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_tzpath.py',
   'PYMODULE'),
  ('zoneinfo._zoneinfo',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\zoneinfo\\_zoneinfo.py',
   'PYMODULE')])
