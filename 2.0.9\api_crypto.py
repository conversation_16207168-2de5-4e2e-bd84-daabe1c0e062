import os
import base64
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

# 配置日志
logger = logging.getLogger(__name__)

class ApiCrypto:
    """API密钥加密解密工具类，使用AES-GCM模式"""
    
    def __init__(self, password=None):
        """初始化加密工具
        
        Args:
            password: 加密密码，默认为None时使用默认密码
        """
        self._password = password or "www.google.com"
        # 固定的盐值，用于派生密钥
        self._salt = b'api_translator_salt'
        # 派生一个256位的密钥
        self._key = self._derive_key(self._password.encode(), self._salt)
        
    def _derive_key(self, password, salt):
        """派生加密密钥
        
        Args:
            password: 密码
            salt: 盐值
            
        Returns:
            bytes: 派生的密钥
        """
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,  # 256位密钥
            salt=salt,
            iterations=100000,
        )
        return kdf.derive(password)
    
    def encrypt(self, api_key):
        """加密API密钥
        
        Args:
            api_key: 原始API密钥
            
        Returns:
            str: Base64编码的加密API密钥
        """
        if not api_key:
            return ""
            
        try:
            # 生成随机的随机数（每次加密都不同）
            nonce = os.urandom(12)  # 96位随机数
            
            # 创建AES-GCM加密器
            aesgcm = AESGCM(self._key)
            
            # 加密API密钥
            ciphertext = aesgcm.encrypt(nonce, api_key.encode(), None)
            
            # 将随机数和密文拼接后进行Base64编码
            encrypted = base64.b64encode(nonce + ciphertext).decode('utf-8')
            
            logger.debug(f"API密钥已加密，长度: {len(encrypted)}")
            return encrypted
        except Exception as e:
            logger.error(f"加密API密钥失败: {type(e).__name__}")
            return ""
    
    def decrypt(self, encrypted_api_key):
        """解密API密钥
        
        Args:
            encrypted_api_key: Base64编码的加密API密钥
            
        Returns:
            str: 原始API密钥
        """
        if not encrypted_api_key:
            return ""
            
        try:
            # 解码Base64
            data = base64.b64decode(encrypted_api_key)
            
            # 分离随机数和密文
            nonce = data[:12]  # 前12字节是随机数
            ciphertext = data[12:]  # 剩余部分是密文
            
            # 创建AES-GCM解密器
            aesgcm = AESGCM(self._key)
            
            # 解密
            plaintext = aesgcm.decrypt(nonce, ciphertext, None)
            
            api_key = plaintext.decode('utf-8')
            logger.debug("API密钥解密成功")
            return api_key
        except Exception as e:
            logger.error(f"解密API密钥失败: {type(e).__name__}")
            return ""
            
    def is_encrypted(self, api_key):
        """检查API密钥是否已加密
        
        Args:
            api_key: 待检查的API密钥
            
        Returns:
            bool: 是否已加密
        """
        if not api_key:
            return False
            
        try:
            # 尝试解码Base64
            base64.b64decode(api_key)
            
            # 尝试解密
            decrypted = self.decrypt(api_key)
            
            # 如果能解密成功且结果不为空，则认为是加密的
            return bool(decrypted)
        except:
            # 如果解码失败，则不是加密的
            return False


# 创建命令行加解密工具函数
def encrypt_api_key(api_key, password=None):
    """加密API密钥的命令行工具函数
    
    Args:
        api_key: 要加密的API密钥
        password: 加密密码，默认为None
    
    Returns:
        str: 加密后的API密钥
    """
    crypto = ApiCrypto(password)
    encrypted = crypto.encrypt(api_key)
    return encrypted

def decrypt_api_key(encrypted_api_key, password=None):
    """解密API密钥的命令行工具函数
    
    Args:
        encrypted_api_key: 要解密的API密钥
        password: 解密密码，默认为None
    
    Returns:
        str: 解密后的API密钥
    """
    crypto = ApiCrypto(password)
    decrypted = crypto.decrypt(encrypted_api_key)
    return decrypted

# 命令行工具入口
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="API密钥加密解密工具")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("-e", "--encrypt", help="要加密的API密钥")
    group.add_argument("-d", "--decrypt", help="要解密的API密钥")
    parser.add_argument("-p", "--password", help="加密/解密密码，不提供则使用默认密码")
    
    args = parser.parse_args()
    
    if args.encrypt:
        result = encrypt_api_key(args.encrypt, args.password)
        print(f"加密结果: {result}")
    elif args.decrypt:
        result = decrypt_api_key(args.decrypt, args.password)
        print(f"解密结果: {result}") 