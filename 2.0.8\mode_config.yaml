﻿# 语言模式配置文件
# 版本：2.0.7

# 语气助词配置
tone_particles:
  zh: "[哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]"  # 中文语气助词
  ko: "[ㅋ|ㅎ|아|네|헤|ㅜ]"  # 韩文语气助词
  ja: "[ｗ笑]"  # 日文语气助词
  en: "[lol|haha|hehe|yeah]"  # 英文语气助词
  vi: "[hihi|hehe|ạ|nhé|nha|á|ấy]"  # 越南语气助词
  fr: "[lol|haha|hein|quoi|eh bien]"  # 法语语气助词
  de: "[haha|hehe|ne|ja|doch|mal]"  # 德语语气助词
  es: "[jaja|jeje|eh|pues|vale|no]"  # 西班牙语语气助词
  ru: "[хаха|хехе|ну|да|же]"  # 俄语语气助词
  pt: "[haha|hehe|né|pois|então]"  # 葡萄牙语语气助词
  ar: "[هههه|والله|يعني|طيب]"  # 阿拉伯语语气助词
  it: "[haha|hehe|eh|beh|dai|cioè]"  # 意大利语语气助词
  th: "[ฮ่าฮ่า|ฮิฮิ|นะ|ค่ะ|ครับ|จ้า|ล่ะ]"  # 泰语语气助词
  km: "[ហាហា|ហិហិ|ណា|ទេ|ហើយ]"  # 柬埔寨语语气助词

# 翻译模式配置
translation_modes:
  1:  # 模式1：中韩平语
    source_lang: 中文
    target_lang: 韩文
    style: 平语
    default_lang: 中文
    source_code: zh
    target_code: ko
  2:  # 模式2：中韩敬语
    source_lang: 中文
    target_lang: 韩文
    style: 敬语
    default_lang: 中文
    source_code: zh
    target_code: ko
  3:  # 模式3：中日敬语
    source_lang: 中文
    target_lang: 日文
    style: 敬语
    default_lang: 中文
    source_code: zh
    target_code: ja
  4:  # 模式4：中日平语
    source_lang: 中文
    target_lang: 日文
    style: 平语
    default_lang: 中文
    source_code: zh
    target_code: ja
  5:  # 模式5：中英
    source_lang: 中文
    target_lang: 英文
    style: "自然国际化"
    default_lang: 中文
    source_code: zh
    target_code: en
  6:  # 模式6：中越
    source_lang: 中文
    target_lang: 越南文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: vi
  7:  # 模式7：中法
    source_lang: 中文
    target_lang: 法文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: fr
  8:  # 模式8：中德
    source_lang: 中文
    target_lang: 德文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: de
  9:  # 模式9：中西
    source_lang: 中文
    target_lang: 西班牙文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: es
  10:  # 模式10：中俄
    source_lang: 中文
    target_lang: 俄文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: ru
  11:  # 模式11：中葡
    source_lang: 中文
    target_lang: 葡萄牙文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: pt
  12:  # 模式12：中阿
    source_lang: 中文
    target_lang: 阿拉伯文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: ar
  13:  # 模式13：中意
    source_lang: 中文
    target_lang: 意大利文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: it
  14:  # 模式14：中泰
    source_lang: 中文
    target_lang: 泰文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: th
  15:  # 模式15：中柬
    source_lang: 中文
    target_lang: 柬埔寨文
    style: "自然"
    default_lang: 中文
    source_code: zh
    target_code: km

# 语言特征配置
language_features:
  zh:  # 中文特征
    pattern: "[一-鿿]"            # 汉字Unicode范围
    exclusive:  # 排除特征
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 汉字  # 描述
    question_pattern: "[?？]"
    exclamation_pattern: "[!！]"
    unique_features:
      - 的
      - 了
      - 是
      - 在
      - 不
      - 我
      - 有
      - 和
      - 你
      - 这
  ko:  # 韩文特征
    pattern: "[가-힯]"            # 韩文谚文Unicode范围
    exclusive:
      - '[぀-ゟ゠-ヿ]'
    desc: 韩文谚文  # 描述
    question_pattern: "[?？]"
    exclamation_pattern: "[!！]"
    unique_features:
      - 요
      - 니다
      - 이다
      - 음
      - 군요
      - 네요
      - 아요
      - 어요
      - ㅋㅋ
      - ㅎㅎ
  ja:  # 日文特征
    pattern: "[぀-ゟ゠-ヿ]"                      # 日文假名Unicode范围
    exclusive:
      - '[가-힯]'
    desc: 日文假名  # 描述
    question_pattern: "[?？]"
    exclamation_pattern: "[!！]"
    unique_features:
      - です
      - ます
      - ござい
      - だ
      - たい
      - ません
      - ました
      - の
      - へ
      - を
  en:  # 英文特征
    pattern: "[A-Za-z]"  # 英文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 英文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - the
      - and
      - of
      - to
      - a
      - in
      - is
      - you
      - that
      - it
  vi:  # 越南文特征
    pattern: "[A-Za-zÀ-ỹ]"  # 越南文字母范围（包括带音调符号的拉丁字母）
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 越南文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - của
      - là
      - và
      - một
      - có
      - những
      - được
      - không
      - trong
      - để
  fr:  # 法文特征
    pattern: "[A-Za-zÀ-ÿ]"  # 法文字母范围（包括带音调符号的拉丁字母）
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 法文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - les
      - des
      - est
      - dans
      - pour
      - pas
      - mais
      - avec
      - sont
      - vous
  de:  # 德文特征
    pattern: "[A-Za-zÄäÖöÜüß]"  # 德文字母范围（包括变音符号）
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 德文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - der
      - die
      - das
      - und
      - ist
      - in
      - den
      - von
      - zu
      - mit
  es:  # 西班牙文特征
    pattern: "[A-Za-zÁáÉéÍíÓóÚúÜüÑñ]"  # 西班牙文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 西班牙文拉丁字母  # 描述
    question_pattern: "[?¿]"
    exclamation_pattern: "[!¡]"
    unique_features:
      - el
      - la
      - los
      - las
      - que
      - en
      - y
      - es
      - para
      - por
  ru:  # 俄文特征
    pattern: "[А-Яа-я]"  # 俄文西里尔字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 俄文西里尔字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - и
      - в
      - не
      - на
      - я
      - что
      - быть
      - с
      - он
      - это
  pt:  # 葡萄牙文特征
    pattern: "[A-Za-zÁáÂâÃãÀàÇçÉéÊêÍíÓóÔôÕõÚú]"  # 葡萄牙文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 葡萄牙文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - o
      - a
      - e
      - de
      - em
      - para
      - com
      - não
      - um
      - uma
  ar:  # 阿拉伯文特征
    pattern: "[؀-ۿ]"            # 阿拉伯文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 阿拉伯文字母  # 描述
    question_pattern: "[؟]"
    exclamation_pattern: "[!]"
    unique_features:
      - في
      - من
      - على
      - إلى
      - أن
      - هذا
      - هذه
      - هو
      - هي
      - أنا
  it:  # 意大利文特征
    pattern: "[A-Za-zÀàÈèÉéÌìÍíÒòÓóÙùÚú]"  # 意大利文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 意大利文拉丁字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - il
      - la
      - di
      - e
      - che
      - un
      - per
      - è
      - in
      - non
  th:  # 泰文特征
    pattern: "[฀-๿]"            # 泰文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 泰文字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"
    unique_features:
      - ที่
      - ของ
      - และ
      - ใน
      - จะ
      - ไม่
      - เป็น
      - ได้
      - มี
      - การ
  km:  # 柬埔寨文特征
    pattern: "[ក-៿]"            # 柬埔寨文字母范围
    exclusive:  # 排除特征
      - '[一-鿿]'
      - '[가-힯]'
      - '[぀-ゟ゠-ヿ]'
    desc: 柬埔寨文字母  # 描述
    question_pattern: "[?]"
    exclamation_pattern: "[!]"

# 特殊语言组配置
    unique_features:
      - នៅ
      - ក្នុង
      - និង
      - ការ
      - របស់
      - ទៅ
      - មាន
      - ពី
      - ដែល
      - ជា
special_language_groups:
  cjk:
    languages:
      - zh
      - ja
      - ko
    strict_detection: false
    desc: 中日韩语言组
  latin:
    languages:
      - nl
      - en
      - pt
      - da
      - sv
      - no
      - vi
      - es
      - de
      - it
      - fi
      - fr
    strict_detection: false
    desc: 拉丁语系
  indic:
    languages:
      - mr
      - kn
      - hi
      - pa
      - bn
      - gu
      - ta
      - te
      - ml
    strict_detection: false
    desc: 印度语系
  semitic:
    languages:
      - mt
      - ar
      - ti
      - am
      - he
    strict_detection: false
    desc: 闪米特语系
  southeast_asian:
    languages:
      - th
      - ms
      - my
      - id
      - lo
      - tl
      - vi
      - km
    strict_detection: false
    desc: 东南亚语系

# 特殊语言对配置
  slavic:
    languages:
      - be
      - bs
      - hr
      - sl
      - sr
      - bg
      - ru
      - cs
      - sk
      - mk
      - pl
      - uk
    strict_detection: false
    desc: 斯拉夫语系
  germanic:
    languages:
      - en
      - de
      - nl
      - sv
      - da
      - no
    strict_detection: false
    desc: 日耳曼语族
  romance:
    languages:
      - fr
      - es
      - it
      - pt
      - ro
      - ca
    strict_detection: false
    desc: 罗曼语族
  baltic:
    languages:
      - lt
      - lv
    strict_detection: false
    desc: 波罗的海语族
  uralic:
    languages:
      - fi
      - et
      - hu
    strict_detection: false
    desc: 乌拉尔语族
  turkic:
    languages:
      - tr
      - az
      - kk
      - ky
      - uz
    strict_detection: false
    desc: 突厥语族
  dravidian:
    languages:
      - ta
      - te
      - ml
      - kn
    strict_detection: false
    desc: 德拉维族语系
  sino_tibetan:
    languages:
      - zh
      - bo
      - my
    strict_detection: false
    desc: 汉藏语系
  austronesian:
    languages:
      - id
      - ms
      - tl
      - jv
    strict_detection: false
    desc: 南岛语系
  afro_asiatic:
    languages:
      - ar
      - he
      - am
      - ha
    strict_detection: false
    desc: 亚非语系
  tai_kadai:
    languages:
      - th
      - lo
    strict_detection: false
    desc: 壮侗语系
  austroasiatic:
    languages:
      - vi
      - km
    strict_detection: false
    desc: 南亚语系
special_language_pairs:
  "*-*":
    max_attempts: 2
    desc: 通用语言对配置
    skip_source_detection: false
    min_char_ratio: 0.2
  zh-ko:
    max_attempts: 2
    desc: zh-ko互译配置
    skip_source_detection: true
    min_char_ratio: 0.3
  zh-ja:
    max_attempts: 2
    desc: zh-ja互译配置
    skip_source_detection: true
    min_char_ratio: 0.3
  zh-en:
    max_attempts: 2
    desc: zh-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-vi:
    max_attempts: 2
    desc: zh-vi互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-fr:
    max_attempts: 2
    desc: zh-fr互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-de:
    max_attempts: 2
    desc: zh-de互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-es:
    max_attempts: 2
    desc: zh-es互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-ru:
    max_attempts: 2
    desc: zh-ru互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-pt:
    max_attempts: 2
    desc: zh-pt互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-ar:
    max_attempts: 2
    desc: zh-ar互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-it:
    max_attempts: 2
    desc: zh-it互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-th:
    max_attempts: 2
    desc: zh-th互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  zh-km:
    max_attempts: 2
    desc: zh-km互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-latin:
    desc: cjk与latin语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-slavic:
    desc: cjk与slavic语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-indic:
    desc: cjk与indic语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  cjk-semitic:
    desc: cjk与semitic语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  latin-cjk:
    desc: latin与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  latin-slavic:
    desc: latin与slavic语言组互译配置
    min_char_ratio: 0.1
  latin-indic:
    desc: latin与indic语言组互译配置
    min_char_ratio: 0.1
  latin-semitic:
    desc: latin与semitic语言组互译配置
    min_char_ratio: 0.1
  slavic-cjk:
    desc: slavic与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  slavic-latin:
    desc: slavic与latin语言组互译配置
    min_char_ratio: 0.1
  slavic-indic:
    desc: slavic与indic语言组互译配置
    min_char_ratio: 0.1
  slavic-semitic:
    desc: slavic与semitic语言组互译配置
    min_char_ratio: 0.1
  indic-cjk:
    desc: indic与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  indic-latin:
    desc: indic与latin语言组互译配置
    min_char_ratio: 0.1
  indic-slavic:
    desc: indic与slavic语言组互译配置
    min_char_ratio: 0.1
  indic-semitic:
    desc: indic与semitic语言组互译配置
    min_char_ratio: 0.1
  semitic-cjk:
    desc: semitic与cjk语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  semitic-latin:
    desc: semitic与latin语言组互译配置
    min_char_ratio: 0.1
  semitic-slavic:
    desc: semitic与slavic语言组互译配置
    min_char_ratio: 0.1
  semitic-indic:
    desc: semitic与indic语言组互译配置
    min_char_ratio: 0.1
  cjk-*:
    desc: cjk语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-cjk":
    desc: 外部语言翻译到cjk语言组
    min_char_ratio: 0.15
    max_attempts: 2
  latin-*:
    desc: latin语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-latin":
    desc: 外部语言翻译到latin语言组
    min_char_ratio: 0.15
    max_attempts: 2
  slavic-*:
    desc: slavic语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-slavic":
    desc: 外部语言翻译到slavic语言组
    min_char_ratio: 0.15
    max_attempts: 2
  indic-*:
    desc: indic语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-indic":
    desc: 外部语言翻译到indic语言组
    min_char_ratio: 0.15
    max_attempts: 2
  semitic-*:
    desc: semitic语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-semitic":
    desc: 外部语言翻译到semitic语言组
    min_char_ratio: 0.15
    max_attempts: 2
  cjk-southeast_asian:
    desc: cjk与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-cjk:
    desc: 东南亚语言组与cjk互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  latin-southeast_asian:
    desc: latin与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-latin:
    desc: 东南亚语言组与latin互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  slavic-southeast_asian:
    desc: slavic与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-slavic:
    desc: 东南亚语言组与slavic互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  indic-southeast_asian:
    desc: indic与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-indic:
    desc: 东南亚语言组与indic互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  semitic-southeast_asian:
    desc: semitic与东南亚语言组互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-semitic:
    desc: 东南亚语言组与semitic互译配置
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  southeast_asian-*:
    desc: 东南亚语言组对外互译
    min_char_ratio: 0.15
    max_attempts: 2
  "*-southeast_asian":
    desc: 外部语言翻译到东南亚语言组
    min_char_ratio: 0.15
    max_attempts: 2
  ja-ko:
    max_attempts: 2
    desc: ja-ko互译配置
    skip_source_detection: true
    min_char_ratio: 0.3
  ko-en:
    max_attempts: 2
    desc: ko-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  ja-en:
    max_attempts: 2
    desc: ja-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  en-fr:
    max_attempts: 2
    desc: en-fr互译配置
    skip_source_detection: false
    min_char_ratio: 0.3
    check_tone: true
  en-de:
    max_attempts: 2
    desc: en-de互译配置
    skip_source_detection: false
    min_char_ratio: 0.3
    check_tone: true
  en-es:
    max_attempts: 2
    desc: en-es互译配置
    skip_source_detection: false
    min_char_ratio: 0.3
    check_tone: true
  en-ru:
    max_attempts: 2
    desc: en-ru互译配置
    skip_source_detection: false
    min_char_ratio: 0.25
    allow_short_text_mismatch: true
  fr-es:
    max_attempts: 2
    desc: fr-es互译配置
    skip_source_detection: false
    min_char_ratio: 0.4
    check_tone: true
  fr-de:
    max_attempts: 2
    desc: fr-de互译配置
    skip_source_detection: false
    min_char_ratio: 0.4
    check_tone: true
  th-en:
    max_attempts: 2
    desc: th-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  vi-en:
    max_attempts: 2
    desc: vi-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.2
    check_tone: true
  id-en:
    max_attempts: 2
    desc: id-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.3
    check_tone: true
  hi-en:
    max_attempts: 2
    desc: hi-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  th-vi:
    max_attempts: 2
    desc: th-vi互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  id-ms:
    max_attempts: 2
    desc: id-ms互译配置
    skip_source_detection: true
    min_char_ratio: 0.4
    check_tone: true
  ar-en:
    max_attempts: 2
    desc: ar-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  he-en:
    max_attempts: 2
    desc: he-en互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  ar-he:
    max_attempts: 2
    desc: ar-he互译配置
    skip_source_detection: false
    min_char_ratio: 0.3
    check_tone: true
  cjk-european:
    max_attempts: 2
    desc: 东亚-欧洲语言互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  european-southeast_asian:
    max_attempts: 2
    desc: 欧洲-东南亚语言互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  european-indic:
    max_attempts: 2
    desc: 欧洲-南亚语言互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
  european-semitic:
    max_attempts: 2
    desc: 欧洲-闪米特语言互译配置
    skip_source_detection: false
    min_char_ratio: 0.15
    allow_short_text_mismatch: true
pair_disambiguation_rules:
  ko-zh:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-zh:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-ko:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    ko_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  en-fr:
    enabled: true
    en_specific_ratio_threshold: 0.25
    fr_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  en-de:
    enabled: true
    en_specific_ratio_threshold: 0.25
    de_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  en-es:
    enabled: true
    en_specific_ratio_threshold: 0.25
    es_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  fr-es:
    enabled: true
    fr_specific_ratio_threshold: 0.25
    es_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  fr-de:
    enabled: true
    fr_specific_ratio_threshold: 0.25
    de_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  zh-en:
    enabled: true
    zh_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-en:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-en:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  hi-en:
    enabled: true
    hi_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-en:
    enabled: true
    th_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-vi:
    enabled: true
    th_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 1.8
  id-ms:
    enabled: true
    id_specific_ratio_threshold: 0.15
    ms_specific_ratio_threshold: 0.15
    feature_dominance_ratio: 1.5
  hi-bn:
    enabled: true
    hi_specific_ratio_threshold: 0.25
    bn_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  hi-ta:
    enabled: true
    hi_specific_ratio_threshold: 0.25
    ta_specific_ratio_threshold: 0.25
    feature_dominance_ratio: 1.8
  ar-he:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    he_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-ur:
    enabled: true
    ar_specific_ratio_threshold: 0.2
    ur_specific_ratio_threshold: 0.2
    feature_dominance_ratio: 1.5
  ru-it:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    it_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-ja:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    ja_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-ar:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    ar_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-ko:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    ko_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-th:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    th_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-es:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    es_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-pt:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-de:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-zh:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-vi:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-km:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ru-fr:
    enabled: true
    ru_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-ja:
    enabled: true
    it_specific_ratio_threshold: 0.3
    ja_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-ar:
    enabled: true
    it_specific_ratio_threshold: 0.3
    ar_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-ko:
    enabled: true
    it_specific_ratio_threshold: 0.3
    ko_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-th:
    enabled: true
    it_specific_ratio_threshold: 0.3
    th_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-en:
    enabled: true
    it_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-es:
    enabled: true
    it_specific_ratio_threshold: 0.3
    es_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-pt:
    enabled: true
    it_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-de:
    enabled: true
    it_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-zh:
    enabled: true
    it_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-vi:
    enabled: true
    it_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-km:
    enabled: true
    it_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  it-fr:
    enabled: true
    it_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-ar:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    ar_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-th:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    th_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-en:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-es:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    es_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-pt:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-de:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-vi:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-km:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ja-fr:
    enabled: true
    ja_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-ko:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    ko_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-th:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    th_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-es:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    es_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-pt:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-de:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-zh:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-vi:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-km:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ar-fr:
    enabled: true
    ar_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-th:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    th_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-en:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    en_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-es:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    es_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-pt:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-de:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-vi:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-km:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  ko-fr:
    enabled: true
    ko_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-es:
    enabled: true
    th_specific_ratio_threshold: 0.3
    es_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-pt:
    enabled: true
    th_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-de:
    enabled: true
    th_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-zh:
    enabled: true
    th_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-km:
    enabled: true
    th_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  th-fr:
    enabled: true
    th_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  en-pt:
    enabled: true
    en_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  en-vi:
    enabled: true
    en_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  en-km:
    enabled: true
    en_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  es-pt:
    enabled: true
    es_specific_ratio_threshold: 0.3
    pt_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  es-de:
    enabled: true
    es_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  es-zh:
    enabled: true
    es_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  es-vi:
    enabled: true
    es_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  es-km:
    enabled: true
    es_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  pt-de:
    enabled: true
    pt_specific_ratio_threshold: 0.3
    de_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  pt-zh:
    enabled: true
    pt_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  pt-vi:
    enabled: true
    pt_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  pt-km:
    enabled: true
    pt_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  pt-fr:
    enabled: true
    pt_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  de-zh:
    enabled: true
    de_specific_ratio_threshold: 0.3
    zh_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  de-vi:
    enabled: true
    de_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  de-km:
    enabled: true
    de_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  zh-vi:
    enabled: true
    zh_specific_ratio_threshold: 0.3
    vi_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  zh-km:
    enabled: true
    zh_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  zh-fr:
    enabled: true
    zh_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  vi-km:
    enabled: true
    vi_specific_ratio_threshold: 0.3
    km_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  vi-fr:
    enabled: true
    vi_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
  km-fr:
    enabled: true
    km_specific_ratio_threshold: 0.3
    fr_specific_ratio_threshold: 0.3
    feature_dominance_ratio: 2.0
