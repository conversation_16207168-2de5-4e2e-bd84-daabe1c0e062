2025-05-31 09:20:59,861 - INFO - 这是一条信息日志，包含关键词：缓存
2025-05-31 09:20:59,862 - WARNING - 这是一条警告日志，包含关键词：网络
2025-05-31 09:20:59,862 - ERROR - 这是一条错误日志，包含关键词：API
2025-05-31 09:20:59,862 - CRITICAL - 这是一条严重错误日志，包含关键词：翻译
2025-05-31 09:20:59,863 - INFO - 中文日志测试：翻译功能正常工作
2025-05-31 09:20:59,864 - WARNING - 中文警告：网络连接可能不稳定
2025-05-31 09:20:59,864 - ERROR - 中文错误：API调用失败
2025-05-31 09:20:59,864 - INFO - 特殊字符测试：©®™€£¥§¶†‡•…‰‹›''–—
2025-05-31 09:25:37,451 - INFO - 使用脚本目录作为应用路径: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 09:25:37,457 - WARNING - 主配置文件 c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml 不存在，正在自动生成默认配置...
2025-05-31 09:25:37,458 - INFO - 正在生成默认主配置文件: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:37,460 - INFO - 将从 DEFAULT_CONFIG_TEXT 创建或覆盖配置: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:37,482 - INFO - 主配置文件已保存: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:37,483 - INFO - 默认主配置文件已成功生成: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:48,708 - INFO - API密钥验证成功
2025-05-31 09:25:48,737 - INFO - 主配置文件已保存: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:48,738 - INFO - 已将加密的API密钥保存到配置文件
2025-05-31 09:25:48,739 - INFO - 已成功生成默认配置文件: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:48,753 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 09:25:48,821 - WARNING - 模式配置文件 c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml 不存在，正在生成默认配置...
2025-05-31 09:25:48,823 - INFO - 正在生成默认模式配置文件: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 09:25:48,882 - INFO - 将从 DEFAULT_MODE_CONFIG_TEXT 创建或覆盖模式配置: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 09:25:49,013 - INFO - 模式配置文件已保存: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 09:25:49,015 - INFO - 默认模式配置文件已成功生成: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 09:25:49,016 - INFO - 已成功生成默认模式配置文件: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\mode_config.yaml
2025-05-31 09:25:49,143 - INFO - 语言模式配置已加载。
2025-05-31 09:25:49,144 - INFO - 语言模式配置已加载。
2025-05-31 09:25:49,145 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 09:25:49,145 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 09:25:49,158 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 09:25:49,244 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 09:25:49,837 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 09:25:50,041 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 09:25:50,042 - INFO - API服务正常
2025-05-31 09:25:54,509 - INFO - 主配置文件已保存: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-31 09:25:54,523 - INFO - 已切换到翻译模式 2: 中文-韩文-敬语
2025-05-31 09:26:28,681 - INFO - 使用脚本目录作为应用路径: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-31 09:26:28,701 - INFO - [INIT] 日志系统已初始化，调试模式：关闭
2025-05-31 09:26:28,883 - INFO - 语言模式配置已加载。
2025-05-31 09:26:28,883 - INFO - 语言模式配置已加载。
2025-05-31 09:26:28,884 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-31 09:26:28,884 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-31 09:26:28,889 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: c:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\translation_cache.db
2025-05-31 09:26:28,983 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-31 09:26:29,489 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 09:26:29,490 - INFO - API服务正常
2025-05-31 09:26:29,693 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-31 09:34:21,955 - INFO - 检测到三次空格，触发翻译
2025-05-31 09:34:21,956 - INFO - 【原文】
안녕하세요
2025-05-31 09:34:21,958 - INFO - 检测到原文语言: ko
2025-05-31 09:34:21,958 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-31 09:34:21,980 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 09:34:23,914 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-31 09:34:24,387 - INFO - 【翻译结果】
你好
2025-05-31 09:34:24,388 - INFO - 翻译完成，结果已替换输入框内容
2025-05-31 09:34:24,392 - INFO - [GUI] 隐藏GUI进度指示器
2025-05-31 09:36:25,149 - INFO - 检测到三次空格，触发翻译
2025-05-31 09:36:25,150 - INFO - 【原文】
早上好~祝你度过美好的一天。
2025-05-31 09:36:25,150 - INFO - 检测到原文语言: zh
2025-05-31 09:36:25,151 - INFO - 执行正向翻译为: ko
2025-05-31 09:36:25,151 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-31 09:36:25,162 - INFO - [GUI] 显示GUI进度指示器
2025-05-31 09:36:26,780 - ERROR - API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 09:36:26,780 - WARNING - 故障转移：gemini-2.5-flash-preview-04-17 (思考模型) → gemini-2.0-flash-lite-preview-02-05 (标准模型)
2025-05-31 09:36:26,780 - WARNING - 主模型翻译失败: 翻译失败：API请求超过速率限制 (模型: gemini-2.5-flash-preview-04-17)
2025-05-31 09:36:28,236 - ERROR - API请求失败，状态码: 400 (模型: gemini-2.0-flash-lite-preview-02-05)
2025-05-31 09:36:28,236 - ERROR - 翻译最终失败: 翻译失败：API请求失败，状态码: 400 (模型: gemini-2.0-flash-lite-preview-02-05)。原始文本将恢复到输入框。
2025-05-31 09:36:28,695 - ERROR - 翻译失败: 翻译失败：API请求失败，状态码: 400 (模型: gemini-2.0-flash-lite-preview-02-05)
2025-05-31 09:36:28,719 - INFO - [GUI] 隐藏GUI进度指示器
