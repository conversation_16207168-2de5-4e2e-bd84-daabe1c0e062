import sqlite3
import threading
import time
import os
import logging
from collections import deque
from typing import Dict, Optional, Tuple, Any
import sys
import asyncio

logger = logging.getLogger("translator")

class CacheManager:
    """翻译缓存管理器，支持内存缓存和SQLite本地存储"""

    def __init__(self, config):
        # 获取主程序目录
        if getattr(sys, 'frozen', False):
            # 打包后的exe
            base_dir = os.path.dirname(sys.executable)
        else:
            base_dir = os.path.dirname(os.path.abspath(__file__))
        # 如果路径不是绝对路径，则拼接到主程序目录下
        if not os.path.isabs(config.local_cache_path):
            config.local_cache_path = os.path.join(base_dir, config.local_cache_path)
        self.config = config
        self.memory_cache = {}  # 内存缓存
        self.write_queue = deque()  # 写入队列
        self.lock = threading.RLock()  # 读写锁
        self.running = True  # 控制写入线程运行状态

        # 确保缓存目录存在
        cache_dir = os.path.dirname(config.local_cache_path)
        if cache_dir and not os.path.exists(cache_dir):
            os.makedirs(cache_dir, exist_ok=True)

        # 初始化数据库连接
        try:
            self.conn = sqlite3.connect(config.local_cache_path, check_same_thread=False)
            self._init_db()
        except Exception as e:
            logger.error(f"初始化缓存数据库失败: {e}")
            self.conn = None

        # 启动后台写入线程
        self.writer_thread = threading.Thread(target=self._batch_writer, daemon=True)
        self.writer_thread.start()

    def _init_db(self):
        """初始化数据库表结构"""
        if not self.conn:
            return

        with self.conn:
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS translations (
                    hash_key TEXT PRIMARY KEY,
                    source_text TEXT,
                    target_lang TEXT,
                    translation TEXT,
                    timestamp REAL
                )""")
            self.conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON translations(timestamp)")

    def _batch_writer(self):
        """批量写入线程，定期将队列中的翻译写入数据库"""
        while self.running:
            time.sleep(self.config.cache_write_delay)

            if not self.config.use_local_cache or not self.conn:
                continue

            with self.lock:
                if not self.write_queue:
                    continue

                try:
                    batch = []
                    with self.conn:
                        while self.write_queue and len(batch) < 100:  # 限制批量大小
                            item = self.write_queue.popleft()
                            batch.append((
                                item['hash_key'],
                                item['source_text'],
                                item['target_lang'],
                                item['translation'],
                                time.time()
                            ))

                        if batch:
                            self.conn.executemany(
                                "INSERT OR REPLACE INTO translations VALUES (?,?,?,?,?)",
                                batch
                            )
                except Exception as e:
                    logger.error(f"缓存批量写入失败: {e}")
                    # 出错时，将数据放回队列
                    for item in batch:
                        self.write_queue.append({
                            'hash_key': item[0],
                            'source_text': item[1],
                            'target_lang': item[2],
                            'translation': item[3]
                        })

    def generate_key(self, text: str, target_lang: str, source_lang: Optional[str] = None) -> str:
        """生成缓存键"""
        # 如果提供了源语言，将其包含在键中以提高精确度
        if source_lang:
            return f"{hash(text)}_{source_lang}_{target_lang}"
        return f"{hash(text)}_{target_lang}"

    def get_translation(self, text: str, target_lang: str, source_lang: Optional[str] = None) -> Optional[str]:
        """获取缓存的翻译结果

        Args:
            text: 原文本
            target_lang: 目标语言代码
            source_lang: 源语言代码（可选）

        Returns:
            缓存的翻译结果，如果未命中则返回None
        """
        if not text or not target_lang:
            return None

        key = self.generate_key(text, target_lang, source_lang)

        # 检查内存缓存
        with self.lock:
            if result := self.memory_cache.get(key):
                logger.info("内存缓存命中")
                return result

        # 如果启用了本地缓存，检查数据库
        if self.config.use_local_cache and self.conn:
            try:
                cursor = self.conn.execute(
                    "SELECT translation FROM translations WHERE hash_key = ?",
                    (key,)
                )
                if result := cursor.fetchone():
                    # 更新内存缓存
                    with self.lock:
                        self.memory_cache[key] = result[0]
                    logger.info("本地缓存命中")
                    return result[0]
            except Exception as e:
                logger.error(f"查询本地缓存失败: {e}")

        return None

    def add_translation(self, text: str, target_lang: str, translation: str, source_lang: Optional[str] = None):
        """添加翻译结果到缓存

        Args:
            text: 原文本
            target_lang: 目标语言代码
            translation: 翻译结果
            source_lang: 源语言代码（可选）
        """
        if not text or not target_lang or not translation:
            return

        key = self.generate_key(text, target_lang, source_lang)

        with self.lock:
            # 更新内存缓存
            self.memory_cache[key] = translation

            # 如果启用了本地缓存，加入写入队列
            if self.config.use_local_cache and self.conn:
                self.write_queue.append({
                    'hash_key': key,
                    'source_text': text,
                    'target_lang': target_lang,
                    'translation': translation
                })

        # 执行缓存清理
        self._cleanup_memory_cache()

    async def async_add_translation(self, text: str, target_lang: str, translation: str, source_lang: Optional[str] = None):
        """异步添加翻译结果到缓存

        Args:
            text: 原文本
            target_lang: 目标语言代码
            translation: 翻译结果
            source_lang: 源语言代码（可选）
        """
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, self.add_translation, text, target_lang, translation, source_lang)

    def _cleanup_memory_cache(self):
        """清理内存缓存"""
        with self.lock:
            excess = len(self.memory_cache) - self.config.cache_max_entries
            if excess > 0:
                # 移除多余的缓存条目（先进先出）
                for _ in range(excess):
                    if self.memory_cache:
                        self.memory_cache.pop(next(iter(self.memory_cache)))

    def cleanup_local_cache(self):
        """清理本地缓存中超出限制的条目"""
        if not self.config.use_local_cache or not self.conn:
            return

        try:
            with self.conn:
                # 获取当前条目数
                count = self.conn.execute("SELECT COUNT(*) FROM translations").fetchone()[0]
                excess = count - self.config.cache_max_entries

                if excess > 0:
                    # 删除最旧的条目
                    self.conn.execute("""
                        DELETE FROM translations
                        WHERE hash_key IN (
                            SELECT hash_key FROM translations
                            ORDER BY timestamp ASC
                            LIMIT ?
                        )
                    """, (excess,))
                    logger.info(f"已清理本地缓存中的 {excess} 条旧记录")
        except Exception as e:
            logger.error(f"清理本地缓存失败: {e}")

    def clear_memory_cache(self):
        """仅清除内存缓存"""
        with self.lock:
            self.memory_cache.clear()
        logger.info("已清除内存缓存")

    def clear_all_cache(self):
        """清除所有缓存（内存和本地）"""
        self.clear_memory_cache()

        if self.config.use_local_cache and self.conn:
            try:
                with self.conn:
                    self.conn.execute("DELETE FROM translations")
                logger.info("已清除本地缓存")
            except Exception as e:
                logger.error(f"清除本地缓存失败: {e}")

    def save_pending_changes(self):
        """立即保存所有待处理的缓存写入"""
        if not self.config.use_local_cache or not self.conn:
            return

        with self.lock:
            if not self.write_queue:
                return

            try:
                with self.conn:
                    batch = []
                    while self.write_queue:
                        item = self.write_queue.popleft()
                        batch.append((
                            item['hash_key'],
                            item['source_text'],
                            item['target_lang'],
                            item['translation'],
                            time.time()
                        ))

                    if batch:
                        self.conn.executemany(
                            "INSERT OR REPLACE INTO translations VALUES (?,?,?,?,?)",
                            batch
                        )
                    logger.info(f"已立即保存 {len(batch)} 条缓存记录")
            except Exception as e:
                logger.error(f"立即保存缓存失败: {e}")
                # 错误时将数据放回队列
                for item in batch:
                    self.write_queue.append({
                        'hash_key': item[0],
                        'source_text': item[1],
                        'target_lang': item[2],
                        'translation': item[3]
                    })

    async def async_save_pending_changes(self):
        """异步立即保存所有待处理的缓存写入"""
        loop = asyncio.get_running_loop()
        await loop.run_in_executor(None, self.save_pending_changes)

    def close(self):
        """关闭缓存管理器，保存所有待处理的写入"""
        self.running = False
        self.save_pending_changes()

        if self.conn:
            self.conn.close()
            self.conn = None
        logger.info("缓存管理器已关闭")