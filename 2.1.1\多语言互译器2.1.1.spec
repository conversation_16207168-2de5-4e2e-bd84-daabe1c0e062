# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['语言互译.py'],
    pathex=[],
    binaries=[],
    datas=[('api_crypto.py', '.'), ('config_management.py', '.'), ('log_formatter.py', '.')],
    hiddenimports=['pyautogui', 'keyboard', 'pycld2', 'cryptography', 'colorama'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='多语言互译器2.1.1',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['图标.ico'],
)
